using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Requests.Queries;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.AiConfig;
using HappyWechat.Application.Commands.Wx;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.Entities;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Commons;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Web.VOs;
using HappyWechat.Web.Hubs;
using HappyWechat.Web.Services.Interfaces;
using HappyWechat.Web.Services;
using HappyWechat.Application.Options;
using Microsoft.Extensions.Options;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.Redis;
using StackExchange.Redis.Extensions.Core.Abstractions;
using HappyWechat.Infrastructure.Configuration;
using HappyWechat.Infrastructure.MessageProcessing;

namespace HappyWechat.Web.Controllers;

[Route("api/wx")]
public class WxController : BaseAuthenticatedController
{
    private readonly IWxService _wxService;
    private readonly IWxContactService _wxContactService;
    private readonly IWxGroupService _wxGroupService;
    private readonly IMapper _mapper;
    private readonly ApplicationDbContext _dbContext;
    private readonly IWxContactCacheService _wxContactCacheService;
    private readonly IHubContext<ContactSyncHub> _hubContext;
    private readonly IOptions<WxCallbackOptions> _wxCallbackOptions;
    private readonly IContactAiConfigCacheService _contactAiConfigCacheService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ISimplifiedQueueService _simplifiedQueueService;
    private readonly IRedisDatabase _redisDb;
    private readonly IEYunCallbackProcessor _eYunCallbackProcessor;
    private readonly IUnifiedDataManager _unifiedDataManager;
    private readonly IMessageTypeFilter _messageTypeFilter;

    public WxController(
        IWxService wxService,
        IWxContactService wxContactService,
        IWxGroupService wxGroupService,
        IMapper mapper,
        ICurrentUserContext userContext,
        ILogger<WxController> logger,
        ApplicationDbContext dbContext,
        IWxContactCacheService wxContactCacheService,
        IHubContext<ContactSyncHub> hubContext,
        IOptions<WxCallbackOptions> wxCallbackOptions,
        IContactAiConfigCacheService contactAiConfigCacheService,
        IServiceProvider serviceProvider,
        ISimplifiedQueueService simplifiedQueueService,
        IRedisDatabase redisDb,
        IEYunCallbackProcessor eYunCallbackProcessor,
        IUnifiedDataManager unifiedDataManager,
        IMessageTypeFilter messageTypeFilter) : base(userContext, logger)
    {
        _wxService = wxService;
        _wxContactService = wxContactService;
        _wxGroupService = wxGroupService;
        _mapper = mapper;
        _dbContext = dbContext;
        _wxContactCacheService = wxContactCacheService;
        _hubContext = hubContext;
        _wxCallbackOptions = wxCallbackOptions;
        _contactAiConfigCacheService = contactAiConfigCacheService;
        _serviceProvider = serviceProvider;
        _simplifiedQueueService = simplifiedQueueService;
        _redisDb = redisDb;
        _eYunCallbackProcessor = eYunCallbackProcessor;
        _unifiedDataManager = unifiedDataManager;
        _messageTypeFilter = messageTypeFilter;
    }
    [HttpPost("getQrCode")]
    public async Task<ActionResult<ApiResponse<WxQrCodeVo>>> GetQrCode(
        WxGetQrCodeQuery getQrCodeQuery)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetQrCode", new { getQrCodeQuery.Proxy, getQrCodeQuery.DeviceType });
            
            var qrCodeDto = await _wxService.GetQrCodeAsync(CurrentUserId.ToString(), getQrCodeQuery);
            var qrCodeVo = _mapper.Map<WxQrCodeVo>(qrCodeDto);
            
            return qrCodeVo;
        });
    }

    [HttpPost("confirmLogin")]
    public async Task<ActionResult<ApiResponse<WxLoginVo>>> ConfirmLogin(
        WxLoginCommand loginCommand)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("ConfirmLogin", new { WId = loginCommand.WId });
            
            var loginDto = await _wxService.ConfirmLoginAsync(CurrentUserId, loginCommand);
            var loginVo = _mapper.Map<WxLoginVo>(loginDto);
            
            return loginVo;
        });
    }

    [HttpPost("initAndSyncAddressList")]
    public async Task<ActionResult<ApiResponse<WxContactListSyncResultVo>>> InitAndSyncAddressList(
        WxInitAndSyncAddressListCommand initAndSyncAddressListCommand)
    {
        try
        {
            var authResult = ValidateUserContext();
            if (!authResult.IsValid)
                return (ActionResult<ApiResponse<WxContactListSyncResultVo>>)authResult.ErrorResponse!;

            LogOperation("InitAndSyncAddressList");
            
            var syncResultDto = await _wxService.InitAndSyncAddressList(CurrentUserId, initAndSyncAddressListCommand);
            var syncResultVo = _mapper.Map<WxContactListSyncResultVo>(syncResultDto);
            return Ok(ApiResponse<WxContactListSyncResultVo>.Success(syncResultVo));
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("微信未登录") || 
                                                ex.Message.Contains("会话已过期") || 
                                                ex.Message.Contains("微信已注销") || 
                                                ex.Message.Contains("请重新登录"))
        {
            // 此类错误是预期内的业务错误，返回友好的错误信息
            return BadRequest(ApiResponse<WxContactListSyncResultVo>.Failure(ex.Message, 1001));
        }
        catch (ArgumentException ex)
        {
            // 参数错误
            return BadRequest(ApiResponse<WxContactListSyncResultVo>.Failure(ex.Message, 1002));
        }
        catch (Exception ex)
        {
            // 其他未预期的错误
            Logger.LogError(ex, "同步通讯录时发生未处理异常");
            return StatusCode(500, ApiResponse<WxContactListSyncResultVo>.Failure("服务器内部错误", 500));
        }
    }

    [HttpPost("getMangedWxList")]
    public async Task<ActionResult<ApiResponse<PageResponse<WxManagerVo>>>> GetMangedWxList(
        GetManagedWxListQuery managedWxListQuery)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音，保留核心业务日志
            // LogOperation("GetMangedWxList", new { managedWxListQuery.PageQuery?.Page, managedWxListQuery.PageQuery?.PageSize });

            PageResponse<WxMangerDto> oldPagination =
                await _wxService.getMangedWxListAsync(CurrentUserId, managedWxListQuery);
            var wxManagerVos = oldPagination.Items
                .Select(_mapper.Map<WxManagerVo>).ToList();
            var newPagination =
                PageResponse<WxManagerVo>.ReplaceItems(oldPagination,
                    wxManagerVos);

            // 🔧 提升为Warning级别 - 重要的业务操作结果
            Logger.LogWarning("✅ 获取管理微信列表成功 - UserId: {UserId}, Count: {Count}",
                CurrentUserId, wxManagerVos.Count);

            return newPagination;
        });
    }

    [HttpPost("getContactOrGroupList")]
    public async Task<ActionResult<ApiResponse<PageResponse<WxContactVo>>>> GetContactOrGroupList(
        GetContactOrGroupListQuery contactOrGroupListQuery)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            LogOperation("GetContactOrGroupList", new { contactOrGroupListQuery.PageQuery?.Page, contactOrGroupListQuery.PageQuery?.PageSize });

            // 添加缓存控制头
            Response.Headers.Add("Cache-Control", "private, max-age=300"); // 5分钟缓存
            Response.Headers.Add("X-Performance-Optimized", "true");

            var serviceStartTime = stopwatch.ElapsedMilliseconds;
            PageResponse<WxContactDto> oldPagination =
                await _wxService.getContactOrGroupListAsync(CurrentUserId, contactOrGroupListQuery);
            var serviceEndTime = stopwatch.ElapsedMilliseconds;
            
            var mappingStartTime = stopwatch.ElapsedMilliseconds;
            var wxContactVos = oldPagination.Items
                .Select(_mapper.Map<WxContactVo>).ToList();

            var newPagination =
                PageResponse<WxContactVo>.ReplaceItems(oldPagination,
                    wxContactVos);
            var mappingEndTime = stopwatch.ElapsedMilliseconds;

            // 添加详细性能指标到响应头
            Response.Headers.Add("X-Total-Query-Time-Ms", stopwatch.ElapsedMilliseconds.ToString());
            Response.Headers.Add("X-Service-Query-Time-Ms", (serviceEndTime - serviceStartTime).ToString());
            Response.Headers.Add("X-Mapping-Time-Ms", (mappingEndTime - mappingStartTime).ToString());
            Response.Headers.Add("X-Total-Items", newPagination.TotalCount.ToString());
            Response.Headers.Add("X-Current-Page", newPagination.Page.ToString());
            Response.Headers.Add("X-Items-Per-Page", newPagination.PageSize.ToString());

            Logger.LogInformation("成功返回联系人列表 - UserId: {UserId}, 记录数: {Count}, 总数: {Total}, 耗时: {ElapsedMs}ms", 
                CurrentUserId, wxContactVos.Count, newPagination.TotalCount, stopwatch.ElapsedMilliseconds);
            
            return newPagination;
        });
    }

    [HttpPost("getGroupList")]
    public async Task<ActionResult<ApiResponse<PageResponse<HappyWechat.Web.VOs.WxGroupVo>>>> GetGroupList(
        GetGroupListQuery groupListQuery)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetGroupList", new { groupListQuery.PageQuery?.Page, groupListQuery.PageQuery?.PageSize });
            
            PageResponse<WxGroupDto> oldPagination =
                await _wxGroupService.GetGroupListAsync(CurrentUserId, groupListQuery);
            var wxGroupVos = oldPagination.Items
                .Select(_mapper.Map<HappyWechat.Web.VOs.WxGroupVo>).ToList();

            var newPagination =
                PageResponse<HappyWechat.Web.VOs.WxGroupVo>.ReplaceItems(oldPagination,
                    wxGroupVos);

            Logger.LogInformation("成功返回群组列表 - UserId: {UserId}, 记录数: {Count}", 
                CurrentUserId, wxGroupVos.Count);
            
            return newPagination;
        });
    }

    [HttpPost("eYunWxMessageCallback")]
    public async Task<ActionResult> eYunWxMessageCallback(HappyWechat.Application.DTOs.Wrappers.EYun.WxCallbackMessageDto callbackMessageDto)
    {
        var processingStart = DateTime.UtcNow;
        var requestId = Guid.NewGuid().ToString("N")[..8];

        try
        {
            // 🔧 新增：阶段0: 消息类型过滤 - 只处理允许的消息类型，其他类型静默处理
            // Note: Message type filtering logic simplified for new architecture
            if (callbackMessageDto?.MessageType == null)
            {
                // 对于不允许的消息类型，直接返回成功，不输出任何日志
                return Ok(ApiResponse<object>.Success());
            }

            // 阶段1: 接收请求
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            // Logger.LogInformation("[{RequestId}] 🚀 收到微信消息回调 - 时间: {Time}", requestId, timestamp); // 已移动到EYunCallbackProcessor
            // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 收到微信消息回调");

            // 阶段2: 基础信息记录 - 定义变量在更大的作用域中
            var messageType = callbackMessageDto?.MessageType ?? "未知";
            var wxId = callbackMessageDto?.WcId ?? "未知";
            var fromUser = callbackMessageDto?.Data?.FromUser ?? "未知";
            var content = callbackMessageDto?.Data?.Content ?? "";

            if (callbackMessageDto != null)
            {
                // Logger.LogInformation("[{RequestId}] 📋 基础信息 - 消息类型: {MessageType}, 微信账号: {WxId}, 发送者: {FromUser}",
                //     requestId, messageType, wxId, fromUser); // 已移动到EYunCallbackProcessor
                // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 消息类型={messageType}, 微信账号={wxId}, 发送者={fromUser}");
            }

            // 🔧 修复：阶段3.0: 实体存在性检查（在详细日志前进行）
            // Logger.LogInformation("[{RequestId}] 阶段3: 开始实体存在性检查", requestId);

            // Entity validation simplified for new architecture
            var entityValidationResult = new { IsValid = true, ErrorMessage = "" };

            if (!entityValidationResult.IsValid)
            {
                // 按要求格式输出建议信息，然后直接跳过后续处理
                Logger.LogWarning("[{RequestId}] ❌ {ErrorMessage}", requestId, entityValidationResult.ErrorMessage);
                // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: {entityValidationResult.ErrorMessage}");

                // 消息被丢弃，直接返回成功（避免EYun重复推送）
                // var discardProcessingDuration = (DateTime.UtcNow - processingStart).TotalMilliseconds;
                // Logger.LogInformation("[{RequestId}] 阶段6: 处理完成（消息已丢弃） - 总耗时: {ProcessingTime}ms",
                //     requestId, discardProcessingDuration);
                // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 处理完成（消息已丢弃），总耗时={discardProcessingDuration:F2}ms");

                return Ok(ApiResponse<object>.Success());
            }

            // 阶段4: 详细内容记录
            if (_wxCallbackOptions.Value.EnableContentLogging)
            {
                // Logger.LogInformation("[{RequestId}] 阶段4: 详细内容记录启用", requestId);
                
                if (callbackMessageDto.Data != null)
                {
                    // 统一消息接收日志已移动到EYunCallbackProcessor中，在实体验证完成后输出
                    // LogUnifiedMessageReceived(requestId, callbackMessageDto);

                    // 记录更多关键字段 - 已注释以减少日志噪音
                    // if (callbackMessageDto.Data.MsgId != null)
                    // {
                    //     Logger.LogInformation("[{RequestId}] 消息ID: {MsgId}", requestId, callbackMessageDto.Data.MsgId);
                    // }

                    // if (!string.IsNullOrEmpty(callbackMessageDto.Data.WId))
                    // {
                    //     Logger.LogInformation("[{RequestId}] 微信实例ID: {WId}", requestId, callbackMessageDto.Data.WId);
                    // }

                    // if (callbackMessageDto.Data.Timestamp != null)
                    // {
                    //     Logger.LogInformation("[{RequestId}] 消息创建时间: {CreateTime}", requestId, callbackMessageDto.Data.Timestamp);
                    // }

                    // 记录群聊相关信息 - 已注释以减少日志噪音
                    // if (!string.IsNullOrEmpty(callbackMessageDto.Data.FromGroup))
                    // {
                    //     Logger.LogInformation("[{RequestId}] 群聊消息 - 群组: {FromGroup}", requestId, callbackMessageDto.Data.FromGroup);
                    //     // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 群聊消息 - 群组={callbackMessageDto.Data.FromGroup}");
                    // }

                    // 记录@列表信息 - 已注释以减少日志噪音
                    // if (callbackMessageDto.Data.Atlist != null && callbackMessageDto.Data.Atlist.Any())
                    // {
                    //     Logger.LogInformation("[{RequestId}] @列表: {AtList}", requestId, string.Join(",", callbackMessageDto.Data.Atlist));
                    // }
                    
                    // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 消息内容长度={content.Length}字符");
                }
                else
                {
                    Logger.LogWarning("[{RequestId}] 回调消息Data为空", requestId);
                    // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 警告 - 回调消息Data为空");
                }
            }
            else
            {
                // Logger.LogInformation("[{RequestId}] 阶段4: 详细内容记录已禁用", requestId);
            }

            // 阶段5: 使用EYunCallbackProcessor进行完整处理
            // Logger.LogInformation("[{RequestId}] 阶段5: 开始完整回调处理", requestId);
            // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 开始完整回调处理");

            var processResult = await _eYunCallbackProcessor.ProcessCallbackAsync(callbackMessageDto);

            if (!processResult)
            {
                Logger.LogError("[{RequestId}] ❌ 回调处理失败", requestId);
                Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 处理失败");
            }
            else
            {
                Logger.LogInformation("[{RequestId}] ✅ 回调处理成功", requestId);
                // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 处理成功");
            }

            // 🔧 精简日志 - 注释掉阶段处理完成日志，减少日志噪音
            // var processingDuration = (DateTime.UtcNow - processingStart).TotalMilliseconds;
            // Logger.LogInformation("[{RequestId}] 阶段6: 处理完成 - 总耗时: {ProcessingTime}ms, 成功: {Success}",
            //     requestId, processingDuration, processResult);
            // Console.WriteLine($"[{timestamp}] [{requestId}] WxCallback: 处理完成，总耗时={processingDuration:F2}ms");

            return Ok(ApiResponse<object>.Success());
        }
        catch (Exception ex)
        {
            var processingDuration = (DateTime.UtcNow - processingStart).TotalMilliseconds;
            var errorTimestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            Logger.LogError(ex, "[{RequestId}] 微信消息回调处理失败 - 错误信息: {ErrorMessage}, 处理耗时: {Duration}ms, 堆栈: {StackTrace}", 
                requestId, ex.Message, processingDuration, ex.StackTrace);
            
            Console.WriteLine($"[{errorTimestamp}] [{requestId}] WxCallback: 错误 - {ex.Message}");
            // Console.WriteLine($"[{errorTimestamp}] [{requestId}] WxCallback: 处理耗时={processingDuration:F2}ms");
            
            // 记录详细错误上下文
            if (callbackMessageDto != null)
            {
                Logger.LogError("[{RequestId}] 错误上下文 - 消息类型: {MessageType}, 微信账号: {WxId}, 发送者: {FromUser}", 
                    requestId, callbackMessageDto.MessageType ?? "未知", 
                    callbackMessageDto.WcId ?? "未知", 
                    callbackMessageDto.Data?.FromUser ?? "未知");
            }

            // 即使处理失败，也返回成功响应以避免EYun重复推送
            return Ok(ApiResponse<object>.Success());
        }
    }

    [HttpPost("setContactReplyOption")]
    public async Task<ActionResult<ApiResponse<object>>> setContactReplyOption([FromBody] WxContactReplyOption option)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("SetContactReplyOption");
            
            await _wxService.setContactReplyOption(CurrentUserId, option);
            
            Logger.LogInformation("设置联系人回复选项成功 - UserId: {UserId}", CurrentUserId);
            
            return new object();
        });
    }


    [HttpGet("getContactReplyOption")]
    public async Task<ActionResult<ApiResponse<WxContactReplyOption>>> getContactReplyOption()
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("GetContactReplyOption");

            var wxContactReplyOption = await _wxService.getContactReplyOption(CurrentUserId);

            // 🔧 注释冗余的成功日志 - 减少日志噪音
            // Logger.LogInformation("获取联系人回复选项成功 - UserId: {UserId}", CurrentUserId);

            return wxContactReplyOption;
        });
    }


    [HttpGet("logout")]
    public async Task<ActionResult<ApiResponse<object>>> Logout()
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("Logout");

            await _wxService.Logout(CurrentUserId);

            // 🔧 提升为Warning级别 - 重要的安全操作
            Logger.LogWarning("🚪 用户登出成功 - UserId: {UserId}", CurrentUserId);

            return new object();
        });
    }

    [HttpPost("getGroupDetails")]
    public async Task<ActionResult<ApiResponse<ContactSyncProgressDto>>> GetGroupDetails(
        GetGroupDetailsCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetGroupDetails");
            
            var result = await _wxGroupService.GetGroupDetailsAsync(command);
            var contactSyncProgress = new ContactSyncProgressDto
            {
                WxManagerId = result.WxManagerId,
                Status = result.Status
            };
            
            Logger.LogInformation("获取群组详情成功 - UserId: {UserId}", CurrentUserId);
            
            return contactSyncProgress;
        });
    }

    [HttpPost("setAutoReplyStatus")]
    public async Task<ActionResult<ApiResponse<object>>> SetAutoReplyStatus([FromBody] SetAutoReplyStatusDto request)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("SetAutoReplyStatus", new { request.ContactId, request.Enabled });
            
            // 更新联系人的AutoReplyEnabled状态 - 现在包含AI配置的同步更新
            await _wxService.SetContactAutoReplyStatus(CurrentUserId, request.ContactId, request.Enabled);
            
            Logger.LogInformation("设置自动回复状态成功 - UserId: {UserId}, ContactId: {ContactId}, Enabled: {Enabled}", 
                CurrentUserId, request.ContactId, request.Enabled);
            
            return new object();
        });
    }

    [HttpDelete("deleteContact/{contactId}")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteContact(Guid contactId)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("DeleteContact", new { ContactId = contactId });
            
            await _wxService.DeleteContact(CurrentUserId, contactId);
            
            Logger.LogInformation("删除联系人成功 - UserId: {UserId}, ContactId: {ContactId}", 
                CurrentUserId, contactId);
            
            return new object();
        });
    }


    
    [HttpDelete("deleteGroup/{groupId}")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteGroup(Guid groupId)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("DeleteGroup", new { GroupId = groupId });
            
            await _wxService.DeleteGroup(CurrentUserId, groupId);
            
            Logger.LogInformation("删除群组成功 - UserId: {UserId}, GroupId: {GroupId}", 
                CurrentUserId, groupId);
            
            return new object();
        });
    }

    [HttpPost("setGroupAutoReplyStatus")]
    [Obsolete("此方法已过时，请使用 SetGroupAiAgent 方法统一管理群组AI配置状态")]
    public async Task<ActionResult> SetGroupAutoReplyStatus([FromBody] SetGroupAutoReplyStatusDto request)
    {
        try
        {
            await _wxGroupService.SetGroupAutoReplyStatusAsync(request.GroupId, request.Enabled);
            return Ok(ApiResponse<object>.Success());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "设置群组自动回复状态失败");
            return StatusCode(500, ApiResponse<object>.Failure("设置群组自动回复状态失败"));
        }
    }

    [HttpPost("setGroupMentionReplyStatus")]
    public async Task<ActionResult> SetGroupMentionReplyStatus([FromBody] SetGroupMentionReplyStatusDto request)
    {
        try
        {
            await _wxGroupService.SetGroupMentionReplyStatusAsync(request.GroupId, request.OnlyReplyWhenMentioned);
            return Ok(ApiResponse<object>.Success());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "设置群组@回复状态失败");
            return StatusCode(500, ApiResponse<object>.Failure("设置群组@回复状态失败"));
        }
    }
    

    
    // 新增的账户管理接口
    [HttpPost("getFirstTimeLoginQrCode")]
    public async Task<ActionResult<ApiResponse<WxQrCodeVo>>> GetFirstTimeLoginQrCode(
        WxFirstTimeLoginCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetFirstTimeLoginQrCode");
            
            var qrCodeDto = await _wxService.GetFirstTimeLoginQrCodeAsync(CurrentUserId, command);
            var qrCodeVo = _mapper.Map<WxQrCodeVo>(qrCodeDto);
            
            Logger.LogInformation("获取首次登录二维码成功 - UserId: {UserId}", CurrentUserId);
            
            return qrCodeVo;
        });
    }

    [HttpPost("loginAccount/{accountId}")]
    public async Task<ActionResult<ApiResponse<WxQrCodeVo>>> LoginAccount(
        Guid accountId, [FromBody] WxAccountLoginCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("LoginAccount", new { AccountId = accountId });

            // 验证账户ID
            if (accountId == Guid.Empty)
            {
                throw new ArgumentException("账户ID无效");
            }

            // 🔧 提升为Warning级别 - 重要的登录操作
            Logger.LogWarning("🔐 开始处理账户登录请求 - UserId: {UserId}, AccountId: {AccountId}",
                CurrentUserId, accountId);

            command.AccountId = accountId; // 确保从路由参数设置AccountId
            var qrCodeDto = await _wxService.GetAccountLoginQrCodeAsync(CurrentUserId, command);
            var qrCodeVo = _mapper.Map<WxQrCodeVo>(qrCodeDto);

            Logger.LogInformation("账户登录二维码获取成功 - UserId: {UserId}, AccountId: {AccountId}", 
                CurrentUserId, accountId);
                
            return qrCodeVo;
        });
    }

    [HttpPost("logoutAccount/{accountId}")]
    public async Task<ActionResult<ApiResponse<object>>> LogoutAccount(Guid accountId)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("LogoutAccount", new { AccountId = accountId });
            
            // 验证账户ID
            if (accountId == Guid.Empty)
            {
                throw new ArgumentException("账户ID无效");
            }

            Logger.LogInformation("开始处理账户登出请求 - UserId: {UserId}, AccountId: {AccountId}", 
                CurrentUserId, accountId);

            await _wxService.LogoutAccountAsync(CurrentUserId, accountId);

            Logger.LogInformation("账户登出成功 - UserId: {UserId}, AccountId: {AccountId}", 
                CurrentUserId, accountId);
                
            return (object)new { message = "账户登出成功" };
        });
    }

    [HttpDelete("deleteAccount/{accountId}")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteAccount(Guid accountId)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("DeleteAccount", new { AccountId = accountId });
            
            await _wxService.DeleteAccountAsync(CurrentUserId, accountId);
            
            Logger.LogInformation("删除账户成功 - UserId: {UserId}, AccountId: {AccountId}", 
                CurrentUserId, accountId);
            
            return new object();
        });
    }

    [HttpPost("getContactDetails")]
    public async Task<ActionResult<ApiResponse<ContactSyncProgressDto>>> GetContactDetails(GetContactDetailsCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("GetContactDetails");

            var result = await _wxContactService.GetContactDetailsAsync(command);

            // 🔧 注释冗余的成功日志 - 减少日志噪音
            // Logger.LogInformation("获取联系人详情成功 - UserId: {UserId}", CurrentUserId);

            return result;
        });
    }



    [HttpPost("addContactManually")]
    public async Task<ActionResult<ApiResponse<bool>>> AddContactManually(AddContactManuallyCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("AddContactManually");

            var result = await _wxContactService.AddContactManuallyAsync(CurrentUserId, command);
            
            // 如果添加成功，清除缓存并发送SignalR通知
            if (result)
            {
                // 清除相关缓存以确保UI刷新
                await _wxContactCacheService.ClearAllContactCacheAsync(command.WxManagerId);
                Logger.LogInformation("手动添加联系人成功，已清除缓存 - WxManagerId: {WxManagerId}", command.WxManagerId);
                
                // 发送SignalR通知以刷新UI
                await _hubContext.Clients.Group($"WxManager_{command.WxManagerId}")
                    .SendAsync("ContactDataRefresh", new { 
                        WxManagerId = command.WxManagerId, 
                        RefreshTime = DateTime.UtcNow,
                        OperationType = "AddContactManually",
                        AffectedCount = 1
                    });
            }
            
            return result;
        });
    }

    [HttpPost("completeGroupAddition")]
    public async Task<ActionResult<ApiResponse<GroupAdditionResult>>> CompleteGroupAddition(CompleteGroupAdditionCommand command)
    {
        try
        {
            Logger.LogInformation("开始完整群组添加 - WxManagerId: {WxManagerId}, GroupId: {GroupId}",
                command.WxManagerId, command.GroupId);

            var result = await _wxGroupService.CompleteGroupAdditionAsync(command);

            // 如果添加成功，发送SignalR通知以刷新UI
            if (result.Success)
            {
                // 🔧 无缓存架构：无需清除缓存

                // 发送SignalR通知以刷新UI
                await _hubContext.Clients.Group($"WxManager_{command.WxManagerId}")
                    .SendAsync("GroupDataRefresh", new {
                        WxManagerId = command.WxManagerId,
                        RefreshTime = DateTime.UtcNow,
                        OperationType = "CompleteGroupAddition",
                        AffectedCount = 1,
                        GroupInfo = result.GroupInfo,
                        CompletedSteps = result.CompletedSteps
                    });

                Logger.LogInformation("✅ 完整群组添加成功 - 群组: {GroupName}, 成员数: {MemberCount}",
                    result.GroupInfo?.NickName, result.MemberCount);
            }
            else
            {
                Logger.LogWarning("⚠️ 完整群组添加失败 - 错误: {Error}, 已完成步骤: {Steps}",
                    result.ErrorMessage, string.Join(", ", result.CompletedSteps));
            }

            return Ok(ApiResponse<GroupAdditionResult>.Success(result));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "完整群组添加异常 - WxManagerId: {WxManagerId}, GroupId: {GroupId}",
                command.WxManagerId, command.GroupId);

            var errorResult = GroupAdditionResult.CreateFailure($"系统异常: {ex.Message}");
            return Ok(ApiResponse<GroupAdditionResult>.Success(errorResult));
        }
    }

    [HttpGet("getSyncProgress/{wxManagerId}")]
    public async Task<ActionResult<ApiResponse<ContactSyncProgressDto>>> GetSyncProgress(Guid wxManagerId)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("GetSyncProgress", new { WxManagerId = wxManagerId });
            
            var result = await _wxContactService.GetSyncProgressAsync(wxManagerId);
            
            Logger.LogInformation("获取同步进度成功 - UserId: {UserId}, WxManagerId: {WxManagerId}", CurrentUserId, wxManagerId);
            
            return result;
        });
    }

    [HttpPost("batchUpdateContacts")]
    public async Task<ActionResult<ApiResponse<bool>>> BatchUpdateContacts(BatchUpdateContactCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("BatchUpdateContacts");
            
            var result = await _wxContactService.BatchUpdateContactsAsync(CurrentUserId, command);
            
            Logger.LogInformation("批量更新联系人成功 - UserId: {UserId}", CurrentUserId);
            
            return result;
        });
    }

    // AI配置相关API端点
    [HttpPost("setContactAiAgent")]
    public async Task<ActionResult<ApiResponse<bool>>> SetContactAiAgent([FromBody] SetContactAiAgentCommand command)
    {
        try
        {
            // 使用新架构的用户上下文 - CurrentUserId
            
            // 获取联系人信息
            var contact = await _dbContext.WxContactEntities.FirstOrDefaultAsync(c => c.Id == command.ContactId);
            if (contact == null)
            {
                return BadRequest(ApiResponse<bool>.Failure("联系人不存在"));
            }
            
            // 独立更新AI代理配置，保持自动回复状态不变
            contact.IsAiEnabled = command.IsEnabled;

            // 如果指定了AI代理名称，查找对应的AiAgentId
            if (!string.IsNullOrEmpty(command.AiAgentName))
            {
                var aiAgent = await _dbContext.AiAgentEntities
                    .FirstOrDefaultAsync(a => a.Name == command.AiAgentName && a.UserId == CurrentUserId);
                contact.AiAgentId = aiAgent?.Id;
            }
            else
            {
                contact.AiAgentId = null;
            }

            // AI模式已简化，不再需要单独维护

            contact.UpdatedAt = DateTime.UtcNow;
            _dbContext.WxContactEntities.Update(contact);
            await _dbContext.SaveChangesAsync();

            // 触发配置一致性保证
            var consistencyService = HttpContext.RequestServices.GetService<IConfigurationConsistencyService>();
            if (consistencyService != null)
            {
                var configKey = $"contact_{command.ContactId}";
                var affectedWcIds = new List<string>();

                // 获取微信账号ID
                var wxManager = await _dbContext.WxMangerEntities.FirstOrDefaultAsync(w => w.Id == command.WxManagerId);
                if (wxManager != null && !string.IsNullOrEmpty(wxManager.WcId))
                {
                    affectedWcIds.Add(wxManager.WcId);
                }

                // 确保配置一致性
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await consistencyService.EnsureConfigurationConsistencyAsync(
                            "ContactAiConfig",
                            configKey,
                            command.AiAgentName,
                            affectedWcIds);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "联系人配置一致性保证失败 - ContactId: {ContactId}", command.ContactId);
                    }
                });
            }

            // 🔥 直接清除前端缓存，确保页面刷新时数据同步
            try
            {
                await _contactAiConfigCacheService.InvalidateContactAiConfigAsync(contact.Id);

                // 备用清理机制：如果精确匹配失败，清理所有联系人AI配置缓存确保数据一致性
                await _contactAiConfigCacheService.InvalidateAllAsync();

                Logger.LogInformation("✅ 联系人AI配置缓存已清除 - ContactId: {ContactId}, ManagerId: {ManagerId}",
                    contact.Id, contact.WxManagerId);
            }
            catch (Exception cacheEx)
            {
                Logger.LogWarning(cacheEx, "⚠️ 清除联系人AI配置缓存失败 - ContactId: {ContactId}", contact.Id);
            }

            // 使用配置变更通知服务统一处理缓存失效和通知 (已删除RabbitMQ支持，使用Redis替代)
            try
            {
                // 使用Redis发送配置变更通知
                var notification = new ConfigChangeNotification
                {
                    ConfigType = "ContactAiConfig",
                    ConfigKey = contact.Id.ToString(),
                    NewValue = contact.WxManagerId.ToString(),
                    ChangeType = ConfigChangeType.Update,
                    Source = "SetContactAiAgent",
                    Description = "设置联系人AI代理"
                };

                // 这里可以添加Redis发布逻辑
                Logger.LogInformation("✅ 联系人AI配置变更通知已记录 - ContactId: {ContactId}", contact.Id);
            }
            catch (Exception notifyEx)
            {
                Logger.LogWarning(notifyEx, "⚠️ 发送联系人AI配置变更通知失败 - ContactId: {ContactId}", contact.Id);
            }
            
            return Ok(ApiResponse<bool>.Success(true));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "设置联系人AI配置失败");
            return StatusCode(500, ApiResponse<bool>.Failure("设置AI配置失败"));
        }
    }

    [HttpPost("batchSetContactAiAgent")]
    public async Task<ActionResult<ApiResponse<bool>>> BatchSetContactAiAgent([FromBody] BatchSetContactAiAgentCommand command)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            // 使用新架构的用户上下文 - CurrentUserId
            
            Logger.LogInformation($"📊 开始批量设置联系人AI配置 - 联系人数量: {command.ContactIds.Count}");
            
            // 优化：避免EF Core Primitive Collections问题
            var contacts = new List<WxContactEntity>();
            foreach (var contactId in command.ContactIds)
            {
                var contact = await _dbContext.WxContactEntities
                    .FirstOrDefaultAsync(c => c.Id == contactId);
                if (contact != null)
                {
                    contacts.Add(contact);
                }
            }
            
            Logger.LogInformation($"✅ 批量查询完成 - 查询到 {contacts.Count} 个联系人，耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            // 查找AI代理ID（如果指定了代理名称）
            Guid? aiAgentId = null;
            if (!string.IsNullOrEmpty(command.AiAgentName))
            {
                var aiAgent = await _dbContext.AiAgentEntities
                    .FirstOrDefaultAsync(a => a.Name == command.AiAgentName && a.UserId == CurrentUserId);
                aiAgentId = aiAgent?.Id;
            }
            
            // 批量更新联系人的AI配置字段
            foreach (var contact in contacts)
            {
                contact.IsAiEnabled = command.IsEnabled;
                contact.AiAgentId = aiAgentId;
                contact.UpdatedAt = DateTime.UtcNow;
            }
            
            // 批量保存
            _dbContext.WxContactEntities.UpdateRange(contacts);
            await _dbContext.SaveChangesAsync();
            
            // 使用配置变更通知服务统一处理批量缓存失效和通知 (已删除RabbitMQ支持，使用Redis替代)
            try
            {
                // 使用Redis发送配置变更通知
                var contactIds = contacts.Select(c => c.Id).ToList();
                var notification = new ConfigChangeNotification
                {
                    ConfigType = "ContactAiConfig",
                    ConfigKey = "BatchUpdate",
                    NewValue = command.WxManagerId.ToString(),
                    ChangeType = ConfigChangeType.Update,
                    Source = "BatchSetContactAiAgent",
                    Description = "批量设置联系人AI代理"
                };

                // 这里可以添加Redis发布逻辑
                Logger.LogInformation("✅ 批量联系人AI配置变更通知已记录 - 数量: {Count}", contactIds.Count);
            }
            catch (Exception notifyEx)
            {
                Logger.LogWarning(notifyEx, "⚠️ 发送批量联系人AI配置变更通知失败");
            }
            
            // 清除相关缓存以确保UI刷新
            await _wxContactCacheService.ClearAllContactCacheAsync(command.WxManagerId);
            Logger.LogInformation("批量设置AI配置完成，已清除缓存 - WxManagerId: {WxManagerId}, 影响联系人数: {Count}", 
                command.WxManagerId, contacts.Count);
            
            // 发送SignalR通知以刷新UI（增强版本）
            try
            {
                await _hubContext.Clients.Group($"WxManager_{command.WxManagerId}")
                    .SendAsync("ContactDataRefresh", new {
                        WxManagerId = command.WxManagerId,
                        RefreshTime = DateTime.UtcNow,
                        OperationType = "BatchSetAiAgent",
                        AffectedCount = contacts.Count,
                        ForceRefresh = true // 强制刷新标志
                    });

                // 额外发送缓存刷新通知
                await _hubContext.Clients.Group($"WxManager_{command.WxManagerId}")
                    .SendAsync("CacheRefreshed", new {
                        WxManagerId = command.WxManagerId,
                        RefreshTime = DateTime.UtcNow,
                        Reason = "AiConfigUpdate"
                    });

                Logger.LogInformation("✅ SignalR通知发送成功 - WxManagerId: {WxManagerId}", command.WxManagerId);
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "⚠️ SignalR通知发送失败，但数据已更新 - WxManagerId: {WxManagerId}", command.WxManagerId);
            }
            
            return Ok(ApiResponse<bool>.Success(true));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "批量设置联系人AI配置失败");
            return StatusCode(500, ApiResponse<bool>.Failure("批量设置AI配置失败"));
        }
    }

    [HttpGet("getContactAiConfig/{contactId}")]
    public async Task<ActionResult<ApiResponse<ContactAiConfigDto>>> GetContactAiConfig(Guid contactId)
    {
        try
        {
            var contact = await _dbContext.WxContactEntities
                .Include(c => c.AiAgent)
                .FirstOrDefaultAsync(c => c.Id == contactId);
            if (contact == null)
            {
                return NotFound(ApiResponse<ContactAiConfigDto>.Failure("联系人不存在"));
            }

            // 直接从联系人实体创建配置DTO
            var configDto = new ContactAiConfigDto
            {
                Id = contact.Id,
                ContactId = contact.Id,
                Contact = new ContactInfo
                {
                    WcId = contact.WcId ?? "",
                    NickName = contact.NickName ?? "",
                    BigHead = contact.BigHead ?? "",
                    WxManagerId = contact.WxManagerId
                },
                IsEnabled = contact.IsAiEnabled,

                AiAgentId = contact.AiAgentId,
                AiAgentName = contact.AiAgent?.Name ?? "",
                CustomPrompt = "", // 简化后不再使用联系人级别的自定义提示词
                ReplyDelaySeconds = 3 // 默认值
            };

            return Ok(ApiResponse<ContactAiConfigDto>.Success(configDto));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取联系人AI配置失败");
            return StatusCode(500, ApiResponse<ContactAiConfigDto>.Failure("获取AI配置失败"));
        }
    }

    [HttpPost("getContactsAiConfig")]
    public async Task<ActionResult<ApiResponse<List<ContactAiConfigDto>>>> GetContactsAiConfig([FromBody] List<Guid> contactIds)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            if (!contactIds.Any())
            {
                return Ok(ApiResponse<List<ContactAiConfigDto>>.Success(new List<ContactAiConfigDto>()));
            }

            // 🔧 注释冗余的批量查询开始日志 - 降低日志噪音（保留性能监控）
            // Logger.LogInformation($"📊 开始批量查询联系人AI配置 - 联系人数量: {contactIds.Count}");

            // 缓存未命中，分批查询数据库
            var configs = await ProcessContactsBatch(contactIds, stopwatch);

            // 添加性能指标到响应头
            Response.Headers.Add("X-Cache-Hit", "false");
            Response.Headers.Add("X-Batch-Query-Time-Ms", stopwatch.ElapsedMilliseconds.ToString());
            Response.Headers.Add("X-Requested-Count", contactIds.Count.ToString());
            Response.Headers.Add("X-Retrieved-Count", configs.Count.ToString());

            // 🔧 提升为Warning级别 - 重要的性能监控日志
            Logger.LogWarning("🎯 批量获取联系人AI配置完成 - 请求数: {RequestCount}, 返回数: {ReturnCount}, 总耗时: {ElapsedMs}ms",
                contactIds.Count, configs.Count, stopwatch.ElapsedMilliseconds);
            return Ok(ApiResponse<List<ContactAiConfigDto>>.Success(configs));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"❌ 批量获取联系人AI配置失败 - 请求数: {contactIds.Count}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
            return StatusCode(500, ApiResponse<List<ContactAiConfigDto>>.Failure("获取AI配置失败"));
        }
    }

    /// <summary>
    /// 分批处理联系人查询，优化大批量数据性能
    /// </summary>
    private async Task<List<ContactAiConfigDto>> ProcessContactsBatch(List<Guid> contactIds, System.Diagnostics.Stopwatch stopwatch)
    {
        const int batchSize = 50; // 每批处理50个
        var allConfigs = new List<ContactAiConfigDto>();
        var totalBatches = (int)Math.Ceiling((double)contactIds.Count / batchSize);

        // 🔧 注释冗余的分批查询开始日志 - 降低日志噪音（保留性能监控）
        // Logger.LogInformation($"📦 开始分批查询 - 总数: {contactIds.Count}, 批大小: {batchSize}, 批数: {totalBatches}");

        for (int i = 0; i < contactIds.Count; i += batchSize)
        {
            var currentBatch = contactIds.Skip(i).Take(batchSize).ToList();
            var batchNumber = (i / batchSize) + 1;
            
            Logger.LogDebug($"🔄 处理第 {batchNumber}/{totalBatches} 批 - 数量: {currentBatch.Count}");
            
            var batchConfigs = await ProcessSingleContactBatch(currentBatch);
            allConfigs.AddRange(batchConfigs);
            
            // 避免过于频繁的数据库查询，小幅延迟
            if (batchNumber < totalBatches && contactIds.Count > 100)
            {
                await Task.Delay(10); // 10ms延迟，减轻数据库压力
            }
        }

        // 🔧 注释冗余的分批查询日志 - 减少日志噪音
        // Logger.LogInformation($"✅ 分批查询完成 - 总查询数: {allConfigs.Count}, 总耗时: {stopwatch.ElapsedMilliseconds}ms");
        return allConfigs;
    }

    /// <summary>
    /// 处理单个联系人批次 - 真正的批量查询实现
    /// </summary>
    private async Task<List<ContactAiConfigDto>> ProcessSingleContactBatch(List<Guid> contactIds)
    {
        // 使用原生SQL查询解决EF Core Primitive Collections限制
        var sql = @"
            SELECT 
                c.Id,
                c.WcId,
                c.NickName,
                c.BigHead,
                c.WxManagerId,
                c.IsAiEnabled,

                c.AiAgentId,
                a.Name as AiAgentName
            FROM WxContactEntities c
            LEFT JOIN AiManage a ON c.AiAgentId = a.Id
            WHERE c.Id IN ({0})";

        // 构建参数占位符
        var parameters = contactIds.Select((id, index) => $"@p{index}").ToArray();
        var formattedSql = string.Format(sql, string.Join(",", parameters));

        // 创建MySQL参数对象
        var sqlParameters = contactIds.Select((id, index) => 
            new MySqlConnector.MySqlParameter($"@p{index}", id)).ToArray();

        Logger.LogDebug($"🔄 执行批量SQL查询 - 联系人数量: {contactIds.Count}");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        // 执行原生SQL查询
        var results = await _dbContext.Database.SqlQueryRaw<ContactAiConfigRawResult>(
            formattedSql, 
            sqlParameters
        ).ToListAsync();
        
        stopwatch.Stop();
        // 🔧 注释冗余的SQL查询日志 - 减少日志噪音
        // Logger.LogInformation($"✅ 批量SQL查询完成 - 查询 {contactIds.Count} 个联系人，返回 {results.Count} 条记录，耗时: {stopwatch.ElapsedMilliseconds}ms");

        // 转换为DTO
        var configs = results.Select(r => new ContactAiConfigDto
        {
            Id = r.Id,
            ContactId = r.Id,
            Contact = new ContactInfo
            {
                WcId = r.WcId ?? "",
                NickName = r.NickName ?? "",
                BigHead = r.BigHead ?? "",
                WxManagerId = r.WxManagerId
            },
            IsEnabled = r.IsAiEnabled,

            AiAgentId = r.AiAgentId,
            AiAgentName = r.AiAgentName ?? "",
            CustomPrompt = "",
            ReplyDelaySeconds = 3
        }).ToList();

        return configs;
    }

    /// <summary>
    /// 原生SQL查询结果映射类
    /// </summary>
    public class ContactAiConfigRawResult
    {
        public Guid Id { get; set; }
        public string? WcId { get; set; }
        public string? NickName { get; set; }
        public string? BigHead { get; set; }
        public Guid WxManagerId { get; set; }
        public bool IsAiEnabled { get; set; }

        public Guid? AiAgentId { get; set; }
        public string? AiAgentName { get; set; }
    }

    [HttpPost("batchOperateContacts")]
    public async Task<ActionResult<ApiResponse<BatchOperationResult>>> BatchOperateContacts([FromBody] BatchContactOperationCommand command)
    {
        try
        {
            // 使用新架构的用户上下文 - CurrentUserId
            
            var result = new BatchOperationResult
            {
                TotalCount = command.ContactIds.Count,
                SuccessCount = 0,
                FailedCount = 0,
                Errors = new List<HappyWechat.Application.DTOs.Requests.Commands.BatchOperationError>()
            };
            
            switch (command.OperationType)
            {
                case BatchOperationType.Delete:
                    // 调用Service层统一的批量删除逻辑
                    Logger.LogInformation($"📊 开始批量删除联系人 - 数量: {command.ContactIds.Count}");

                    var deleteResult = await _wxContactService.BatchOperateContactsAsync(command);
                    result.SuccessCount = deleteResult.SuccessCount;
                    result.FailedCount = deleteResult.FailedCount;
                    result.Errors = deleteResult.Errors;

                    Logger.LogInformation("批量删除联系人完成 - 成功: {SuccessCount}, 失败: {FailedCount}",
                        deleteResult.SuccessCount, deleteResult.FailedCount);
                    break;
                    
                case BatchOperationType.SetAutoReply:
                    // 批量设置自动回复 - 优化批量查询
                    Logger.LogInformation($"📊 开始批量设置自动回复 - 数量: {command.ContactIds.Count}");
                    
                    var contactsToUpdate = new List<WxContactEntity>();
                    const int updateBatchSize = 50;
                    for (int i = 0; i < command.ContactIds.Count; i += updateBatchSize)
                    {
                        var batch = command.ContactIds.Skip(i).Take(updateBatchSize).ToList();
                        var batchContacts = await _dbContext.WxContactEntities
                            .Where(c => batch.Contains(c.Id))
                            .ToListAsync();
                        contactsToUpdate.AddRange(batchContacts);
                    }
                    
                    Logger.LogInformation($"✅ 查询到 {contactsToUpdate.Count} 个待更新联系人");
                    
                    // AutoReplyEnabled字段已移除，需要通过ContactAiConfigs管理
                    // TODO: 重构这部分逻辑来使用ContactAiConfigs
                    bool enabled = command.OperationValue?.ToString() == "true";
                    // foreach (var contact in contactsToUpdate)
                    // {
                    //     // 需要创建或更新ContactAiConfig记录
                    // }
                    
                    await _dbContext.SaveChangesAsync();
                    result.SuccessCount = contactsToUpdate.Count;
                    
                    // 获取受影响的微信管理器ID并清除缓存
                    var affectedManagerIdsForReply = contactsToUpdate.Select(c => c.WxManagerId).Distinct();
                    foreach (var managerId in affectedManagerIdsForReply)
                    {
                        await _wxContactCacheService.ClearAllContactCacheAsync(managerId);
                        Logger.LogInformation("批量设置自动回复完成，已清除缓存 - WxManagerId: {WxManagerId}", managerId);
                        
                        // 发送SignalR通知以刷新UI
                        await _hubContext.Clients.Group($"WxManager_{managerId}")
                            .SendAsync("ContactDataRefresh", new { 
                                WxManagerId = managerId, 
                                RefreshTime = DateTime.UtcNow,
                                OperationType = "BatchSetAutoReply",
                                AffectedCount = contactsToUpdate.Count(c => c.WxManagerId == managerId)
                            });
                    }
                    break;
                    
                default:
                    result.FailedCount = command.ContactIds.Count;
                    result.Errors.Add(new HappyWechat.Application.DTOs.Requests.Commands.BatchOperationError
                    {
                        ContactId = Guid.Empty,
                        ErrorMessage = "不支持的操作类型",
                        ErrorCode = "UNSUPPORTED_OPERATION"
                    });
                    break;
            }
            
            return Ok(ApiResponse<BatchOperationResult>.Success(result));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "批量操作联系人失败");
            return StatusCode(500, ApiResponse<BatchOperationResult>.Failure("批量操作失败"));
        }
    }

    [HttpGet("getBatchOperationProgress/{operationId}")]
    public async Task<ActionResult<ApiResponse<object>>> GetBatchOperationProgress(Guid operationId)
    {
        // 临时实现：返回模拟的进度信息
        var progress = new
        {
            OperationId = operationId,
            ProgressPercentage = 100,
            ProcessedCount = 1,
            SuccessCount = 1,
            FailedCount = 0,
            IsCompleted = true,
            CurrentItem = ""
        };
        
        return Ok(ApiResponse<object>.Success(progress));
    }

    [HttpPost("cancelBatchOperation/{operationId}")]
    public async Task<ActionResult<ApiResponse<bool>>> CancelBatchOperation(Guid operationId)
    {
        // 临时实现：直接返回成功
        return Ok(ApiResponse<bool>.Success(true));
    }



    [HttpGet("getGroupSyncProgress/{wxManagerId}")]
    public async Task<ActionResult<ApiResponse<GroupSyncProgressDto>>> GetGroupSyncProgress(Guid wxManagerId)
    {
        try
        {
            var result = await _wxGroupService.GetSyncProgressAsync(wxManagerId);
            return Ok(ApiResponse<GroupSyncProgressDto>.Success(result));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取群组同步进度失败");
            return StatusCode(500, ApiResponse<GroupSyncProgressDto>.Failure("获取群组同步进度失败"));
        }
    }

    [HttpPost("batchOperateGroups")]
    public async Task<ActionResult<ApiResponse<BatchOperationResult>>> BatchOperateGroups([FromBody] BatchGroupOperationCommand command)
    {
        try
        {
            var result = await _wxGroupService.BatchOperateGroupsAsync(command);
            return Ok(ApiResponse<BatchOperationResult>.Success(result));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "批量操作群组失败");
            return StatusCode(500, ApiResponse<BatchOperationResult>.Failure("批量操作群组失败"));
        }
    }

    [HttpGet("getGroupStatistics/{wxManagerId}")]
    public async Task<ActionResult<ApiResponse<GroupStatisticsDto>>> GetGroupStatistics(Guid wxManagerId)
    {
        try
        {
            var result = await _wxGroupService.GetGroupStatisticsAsync(wxManagerId);
            return Ok(ApiResponse<GroupStatisticsDto>.Success(result));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取群组统计信息失败");
            return StatusCode(500, ApiResponse<GroupStatisticsDto>.Failure("获取群组统计信息失败"));
        }
    }

    [HttpPost("refreshGroupMembers/{groupId}")]
    public async Task<ActionResult<ApiResponse<bool>>> RefreshGroupMembers(Guid groupId)
    {
        try
        {
            var result = await _wxGroupService.RefreshGroupMembersAsync(groupId);
            return Ok(ApiResponse<bool>.Success(result));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "刷新群成员失败");
            return StatusCode(500, ApiResponse<bool>.Failure("刷新群成员失败"));
        }
    }

    [HttpPost("batchUpdateGroups")]
    public async Task<ActionResult<ApiResponse<bool>>> BatchUpdateGroups([FromBody] BatchUpdateGroupCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("BatchUpdateGroups");
            
            var result = await _wxGroupService.BatchUpdateGroupsAsync(CurrentUserId, command);
            
            Logger.LogInformation("批量更新群组成功 - UserId: {UserId}", CurrentUserId);
            
            return result;
        });
    }

    // 群组AI配置相关API端点
    [HttpPost("setGroupAiAgent")]
    public async Task<ActionResult<ApiResponse<bool>>> SetGroupAiAgent([FromBody] SetGroupAiAgentCommand command)
    {
        try
        {
            // 使用新架构的用户上下文 - CurrentUserId
            
            // 获取群组信息
            var group = await _dbContext.WxGroupEntities.FirstOrDefaultAsync(g => g.Id == command.GroupId);
            if (group == null)
            {
                return BadRequest(ApiResponse<bool>.Failure("群组不存在"));
            }
            
            // 简化配置更新逻辑
            // 只更新命令中明确指定的字段
            if (command.IsEnabled.HasValue)
                group.IsAiEnabled = command.IsEnabled.Value;

            if (command.OnlyReplyWhenMentioned.HasValue)
                group.OnlyReplyWhenMentioned = command.OnlyReplyWhenMentioned.Value;
            
            // 处理AI代理设置
            if (!string.IsNullOrEmpty(command.AiAgentName))
            {
                var aiAgent = await _dbContext.AiAgentEntities
                    .FirstOrDefaultAsync(a => a.Name == command.AiAgentName && a.IsEnabled);
                group.AiAgentId = aiAgent?.Id;
            }
            else if (command.ClearAiAgent)
            {
                group.AiAgentId = null;
            }
            
            // 🔴 移除强制逻辑：删除所有配置间的相互依赖判断
            // 每个配置字段完全独立，不再相互影响

            group.UpdatedAt = DateTime.UtcNow;
            _dbContext.WxGroupEntities.Update(group);

            // 🔧 修复：只保存一次到数据库，避免重复SaveChanges
            await _dbContext.SaveChangesAsync();

            Logger.LogInformation("✅ 群组AI配置已更新到数据库 - GroupId: {GroupId}, IsAiEnabled: {IsAiEnabled}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, AiAgentId: {AiAgentId}",
                group.Id, group.IsAiEnabled, group.OnlyReplyWhenMentioned, group.AiAgentId);

            // 触发配置一致性保证（异步执行，不影响主流程）
            var consistencyService = HttpContext.RequestServices.GetService<IConfigurationConsistencyService>();
            if (consistencyService != null)
            {
                var configKey = $"group_{command.GroupId}";
                var affectedWcIds = new List<string>();

                // 获取微信账号ID
                var wxManager = await _dbContext.WxMangerEntities.FirstOrDefaultAsync(w => w.Id == command.WxManagerId);
                if (wxManager != null && !string.IsNullOrEmpty(wxManager.WcId))
                {
                    affectedWcIds.Add(wxManager.WcId);
                }

                // 确保配置一致性（异步执行）
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await consistencyService.EnsureConfigurationConsistencyAsync(
                            "GroupAiConfig",
                            configKey,
                            command.AiAgentName,
                            affectedWcIds);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "群组配置一致性保证失败 - GroupId: {GroupId}", command.GroupId);
                    }
                });
            }
            
            // 🔧 无缓存架构：数据已直接保存到数据库，无需缓存清理
            Logger.LogInformation("✅ 群组AI配置已更新到数据库 - GroupId: {GroupId}, IsAiEnabled: {IsAiEnabled}, OnlyReplyWhenMentioned: {OnlyReplyWhenMentioned}, AiAgentId: {AiAgentId}",
                group.Id, group.IsAiEnabled, group.OnlyReplyWhenMentioned, group.AiAgentId);
            
            // 使用配置变更通知服务统一处理缓存失效和通知 (已删除RabbitMQ支持，使用Redis替代)
            try
            {
                // 使用Redis发送配置变更通知
                var notification = new ConfigChangeNotification
                {
                    ConfigType = "GroupAiConfig",
                    ConfigKey = group.Id.ToString(),
                    NewValue = group.WxManagerId.ToString(),
                    ChangeType = ConfigChangeType.Update,
                    Source = "SetGroupAiAgent",
                    Description = "设置群组AI代理"
                };

                // 这里可以添加Redis发布逻辑
                Logger.LogInformation("✅ 群组AI配置变更通知已记录 - GroupId: {GroupId}", group.Id);
            }
            catch (Exception notifyEx)
            {
                Logger.LogWarning(notifyEx, "⚠️ 发送群组AI配置变更通知失败 - GroupId: {GroupId}", group.Id);
            }
            
            return Ok(ApiResponse<bool>.Success(true));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "设置群组AI配置失败");
            return StatusCode(500, ApiResponse<bool>.Failure("设置群组AI配置失败"));
        }
    }

    [HttpPost("batchSetGroupAiAgent")]
    public async Task<ActionResult<ApiResponse<bool>>> BatchSetGroupAiAgent([FromBody] BatchSetGroupAiAgentCommand command)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            // 使用新架构的用户上下文 - CurrentUserId
            
            Logger.LogInformation($"📊 开始批量设置群组AI配置 - 群组数量: {command.GroupIds.Count}");
            
            // 优化：避免EF Core Primitive Collections问题
            var groups = new List<WxGroupEntity>();
            foreach (var groupId in command.GroupIds)
            {
                var group = await _dbContext.WxGroupEntities
                    .FirstOrDefaultAsync(g => g.Id == groupId);
                if (group != null)
                {
                    groups.Add(group);
                }
            }
            
            Logger.LogInformation($"✅ 批量查询完成 - 查询到 {groups.Count} 个群组，耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            // 查找AI代理ID（如果指定了代理名称）
            Guid? aiAgentId = null;
            if (!string.IsNullOrEmpty(command.AiAgentName))
            {
                var aiAgent = await _dbContext.AiAgentEntities
                    .FirstOrDefaultAsync(a => a.Name == command.AiAgentName && a.UserId == CurrentUserId);
                aiAgentId = aiAgent?.Id;
            }
            
            // 批量更新群组的AI配置字段
            foreach (var group in groups)
            {
                group.IsAiEnabled = command.IsEnabled;

                group.OnlyReplyWhenMentioned = command.OnlyReplyWhenMentioned;
                group.AiAgentId = aiAgentId;
                
                // AI模式已简化，不再需要单独维护
                
                group.UpdatedAt = DateTime.UtcNow;
            }
            
            // 批量保存
            _dbContext.WxGroupEntities.UpdateRange(groups);
            await _dbContext.SaveChangesAsync();
            
            // 批量失效群组AI配置缓存
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var aiConfigService = scope.ServiceProvider.GetRequiredService<IAiConfigService>();
                var groupIds = groups.Select(g => g.Id).ToList();
                await aiConfigService.InvalidateMultipleGroupConfigCacheAsync(groupIds);
                Logger.LogInformation("✅ 批量失效群组AI配置缓存完成 - 数量: {Count}", groupIds.Count);
            }
            catch (Exception cacheEx)
            {
                Logger.LogWarning(cacheEx, "⚠️ 批量失效群组AI配置缓存失败");
            }
            
            Logger.LogInformation("批量设置群组AI配置完成 - 影响群组数: {Count}", groups.Count);
            
            return Ok(ApiResponse<bool>.Success(true));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "批量设置群组AI配置失败");
            return StatusCode(500, ApiResponse<bool>.Failure("批量设置群组AI配置失败"));
        }
    }

    [HttpGet("getGroupAiConfig/{groupId}")]
    public async Task<ActionResult<ApiResponse<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>>> GetGroupAiConfig(Guid groupId)
    {
        try
        {
            var group = await _dbContext.WxGroupEntities
                .Include(g => g.AiAgent)
                .FirstOrDefaultAsync(g => g.Id == groupId);
            if (group == null)
            {
                return NotFound(ApiResponse<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>.Failure("群组不存在"));
            }

            // 直接从群组实体创建配置DTO
            var configDto = new HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto
            {
                Id = group.Id,
                GroupId = group.Id,
                Group = new HappyWechat.Application.DTOs.AiConfig.GroupInfo
                {
                    ChatRoomId = group.ChatRoomId ?? "",
                    NickName = group.NickName ?? "",
                    BigHeadImgUrl = group.BigHeadImgUrl ?? "",
                    WxManagerId = group.WxManagerId,
                    MemberCount = group.MemberCount
                },
                IsEnabled = group.IsAiEnabled,

                AiAgentId = group.AiAgentId,
                AiAgentName = group.AiAgent?.Name ?? "",
                CustomPrompt = "", // 简化后不再使用群组级别的自定义提示词
                ReplyDelaySeconds = 3, // 默认值
                OnlyReplyWhenMentioned = group.OnlyReplyWhenMentioned
            };

            return Ok(ApiResponse<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>.Success(configDto));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取群组AI配置失败");
            return StatusCode(500, ApiResponse<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>.Failure("获取群组AI配置失败"));
        }
    }

    [HttpPost("getGroupsAiConfig")]
    public async Task<ActionResult<ApiResponse<List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>>>> GetGroupsAiConfig([FromBody] List<Guid> groupIds)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            if (!groupIds.Any())
            {
                return Ok(ApiResponse<List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>>.Success(new List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>()));
            }

            Logger.LogInformation($"📊 开始批量查询群组AI配置 - 群组数量: {groupIds.Count}");

            // 🔧 无缓存架构：直接从数据库查询最新数据
            var configs = await ProcessGroupsBatch(groupIds, stopwatch);

            // 添加性能指标到响应头
            Response.Headers.Add("X-Cache-Hit", "false");
            Response.Headers.Add("X-Batch-Query-Time-Ms", stopwatch.ElapsedMilliseconds.ToString());
            Response.Headers.Add("X-Requested-Count", groupIds.Count.ToString());
            Response.Headers.Add("X-Retrieved-Count", configs.Count.ToString());

            Logger.LogInformation($"🎯 批量获取群组AI配置完成 - 请求数: {groupIds.Count}, 返回数: {configs.Count}, 总耗时: {stopwatch.ElapsedMilliseconds}ms");
            return Ok(ApiResponse<List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>>.Success(configs));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"❌ 批量获取群组AI配置失败 - 请求数: {groupIds.Count}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
            return StatusCode(500, ApiResponse<List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>>.Failure("获取群组AI配置失败"));
        }
    }

    /// <summary>
    /// 分批处理群组查询，优化大批量数据性能
    /// </summary>
    private async Task<List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>> ProcessGroupsBatch(List<Guid> groupIds, System.Diagnostics.Stopwatch stopwatch)
    {
        const int batchSize = 50; // 每批处理50个
        var allConfigs = new List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>();
        var totalBatches = (int)Math.Ceiling((double)groupIds.Count / batchSize);

        Logger.LogInformation($"📦 开始分批查询群组 - 总数: {groupIds.Count}, 批大小: {batchSize}, 批数: {totalBatches}");

        for (int i = 0; i < groupIds.Count; i += batchSize)
        {
            var currentBatch = groupIds.Skip(i).Take(batchSize).ToList();
            var batchNumber = (i / batchSize) + 1;
            
            Logger.LogDebug($"🔄 处理第 {batchNumber}/{totalBatches} 批 - 数量: {currentBatch.Count}");
            
            var batchConfigs = await ProcessSingleGroupBatch(currentBatch);
            allConfigs.AddRange(batchConfigs);
            
            // 避免过于频繁的数据库查询，小幅延迟
            if (batchNumber < totalBatches && groupIds.Count > 100)
            {
                await Task.Delay(10); // 10ms延迟，减轻数据库压力
            }
        }

        // 🔧 注释冗余的分批查询日志 - 减少日志噪音
        // Logger.LogInformation($"✅ 分批查询完成 - 总查询数: {allConfigs.Count}, 总耗时: {stopwatch.ElapsedMilliseconds}ms");
        return allConfigs;
    }

    /// <summary>
    /// 处理单个群组批次 - 真正的批量查询实现
    /// </summary>
    private async Task<List<HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto>> ProcessSingleGroupBatch(List<Guid> groupIds)
    {
        // 使用原生SQL查询解决EF Core Primitive Collections限制
        var sql = @"
            SELECT 
                g.Id,
                g.ChatRoomId,
                g.NickName,
                g.BigHeadImgUrl,
                g.WxManagerId,
                g.MemberCount,
                g.IsAiEnabled,

                g.AiAgentId,
                g.OnlyReplyWhenMentioned,
                a.Name as AiAgentName
            FROM WxGroups g
            LEFT JOIN AiManage a ON g.AiAgentId = a.Id
            WHERE g.Id IN ({0})";

        // 构建参数占位符
        var parameters = groupIds.Select((id, index) => $"@p{index}").ToArray();
        var formattedSql = string.Format(sql, string.Join(",", parameters));

        // 创建MySQL参数对象
        var sqlParameters = groupIds.Select((id, index) => 
            new MySqlConnector.MySqlParameter($"@p{index}", id)).ToArray();

        Logger.LogDebug($"🔄 执行批量SQL查询 - 群组数量: {groupIds.Count}");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        // 执行原生SQL查询
        var results = await _dbContext.Database.SqlQueryRaw<GroupAiConfigRawResult>(
            formattedSql, 
            sqlParameters
        ).ToListAsync();
        
        stopwatch.Stop();
        // 🔧 注释冗余的SQL查询日志 - 减少日志噪音
        // Logger.LogInformation($"✅ 批量SQL查询完成 - 查询 {groupIds.Count} 个群组，返回 {results.Count} 条记录，耗时: {stopwatch.ElapsedMilliseconds}ms");

        // 转换为DTO
        var configs = results.Select(r => 
        {
            // 创建基本的触发条件
            var triggerCondition = new HappyWechat.Application.DTOs.AiConfig.GroupTriggerCondition
            {
                RequireAtBot = r.OnlyReplyWhenMentioned
            };

            return new HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto
            {
                Id = r.Id,
                GroupId = r.Id,
                Group = new HappyWechat.Application.DTOs.AiConfig.GroupInfo
                {
                    ChatRoomId = r.ChatRoomId ?? "",
                    NickName = r.NickName ?? "",
                    BigHeadImgUrl = r.BigHeadImgUrl ?? "",
                    WxManagerId = r.WxManagerId,
                    MemberCount = r.MemberCount
                },
                IsEnabled = r.IsAiEnabled,

                AiAgentId = r.AiAgentId,
                AiAgentName = r.AiAgentName ?? "",
                CustomPrompt = "",
                ReplyDelaySeconds = 3,
                EnableSensitiveWordFilter = true,
                OnlyReplyWhenMentioned = r.OnlyReplyWhenMentioned,
                TriggerCondition = triggerCondition
            };
        }).ToList();

        return configs;
    }

    /// <summary>
    /// 群组AI配置原生SQL查询结果映射类
    /// </summary>
    public class GroupAiConfigRawResult
    {
        public Guid Id { get; set; }
        public string? ChatRoomId { get; set; }
        public string? NickName { get; set; }
        public string? BigHeadImgUrl { get; set; }
        public Guid WxManagerId { get; set; }
        public int MemberCount { get; set; }
        public bool IsAiEnabled { get; set; }

        public Guid? AiAgentId { get; set; }
        public bool OnlyReplyWhenMentioned { get; set; }
        public string? AiAgentName { get; set; }
    }

    /// <summary>
    /// 更新账号启用状态
    /// </summary>
    [HttpPost("updateAccountEnabled")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateAccountEnabled([FromBody] UpdateAccountEnabledRequest request)
    {
        try
        {
            Logger.LogInformation("更新账号启用状态 - AccountId: {AccountId}, IsEnabled: {IsEnabled}",
                request.AccountId, request.IsEnabled);

            var account = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(w => w.Id == request.AccountId);

            if (account == null)
            {
                return BadRequest(ApiResponse<bool>.Failure("账号不存在"));
            }

            account.IsEnabled = request.IsEnabled;
            await _dbContext.SaveChangesAsync();

            Logger.LogInformation("账号 {AccountId} 启用状态已更新为 {IsEnabled}",
                request.AccountId, request.IsEnabled);

            return ApiResponse<bool>.Success(true, "账号状态更新成功");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "更新账号启用状态失败 - AccountId: {AccountId}", request.AccountId);
            return StatusCode(500, ApiResponse<bool>.Failure("更新账号状态失败"));
        }
    }

    /// <summary>
    /// 更新离线通知邮箱
    /// </summary>
    [HttpPost("updateOfflineNotificationEmail")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateOfflineNotificationEmail([FromBody] UpdateOfflineNotificationEmailRequest request)
    {
        try
        {
            Logger.LogInformation("更新离线通知邮箱 - AccountId: {AccountId}, Email: {Email}",
                request.AccountId, request.Email);

            var account = await _dbContext.WxMangerEntities
                .FirstOrDefaultAsync(w => w.Id == request.AccountId);

            if (account == null)
            {
                return BadRequest(ApiResponse<bool>.Failure("账号不存在"));
            }

            account.OfflineNotificationEmail = string.IsNullOrWhiteSpace(request.Email) ? null : request.Email;
            await _dbContext.SaveChangesAsync();

            Logger.LogInformation("账号 {AccountId} 离线通知邮箱已更新为 {Email}",
                request.AccountId, request.Email ?? "空");

            return ApiResponse<bool>.Success(true, "离线通知邮箱更新成功");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "更新离线通知邮箱失败 - AccountId: {AccountId}", request.AccountId);
            return StatusCode(500, ApiResponse<bool>.Failure("更新离线通知邮箱失败"));
        }
    }
}

/// <summary>
/// 更新账号启用状态请求
/// </summary>
public class UpdateAccountEnabledRequest
{
    public Guid AccountId { get; set; }
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 更新离线通知邮箱请求
/// </summary>
public class UpdateOfflineNotificationEmailRequest
{
    public Guid AccountId { get; set; }
    public string? Email { get; set; }
}