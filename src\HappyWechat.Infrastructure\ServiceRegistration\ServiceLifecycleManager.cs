using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Runtime;

namespace HappyWechat.Infrastructure.ServiceRegistration;

/// <summary>
/// 服务生命周期统一管理器
/// 特性：资源自动释放、内存泄漏防护、服务状态跟踪、优雅关闭
/// </summary>
public sealed class ServiceLifecycleManager : IDisposable
{
    private readonly ILogger<ServiceLifecycleManager> _logger;
    private readonly ConcurrentDictionary<Type, ServiceLifecycleInfo> _serviceTracking = new();
    private readonly ConcurrentDictionary<object, ServiceInstanceInfo> _instanceTracking = new();
    private readonly Timer _cleanupTimer;
    private readonly Timer _memoryMonitorTimer;
    private bool _disposed;

    // 配置常量
    private const int CleanupIntervalSeconds = 300; // 5分钟清理一次
    private const int MemoryMonitorIntervalSeconds = 60; // 1分钟监控一次
    private const long MemoryThresholdBytes = 500 * 1024 * 1024; // 500MB阈值
    private const int MaxInstanceTrackingCount = 10000; // 最大跟踪实例数

    public ServiceLifecycleManager(ILogger<ServiceLifecycleManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 启动定期清理任务
        _cleanupTimer = new Timer(PerformCleanup, null, 
            TimeSpan.FromSeconds(CleanupIntervalSeconds), 
            TimeSpan.FromSeconds(CleanupIntervalSeconds));
        
        // 启动内存监控任务
        _memoryMonitorTimer = new Timer(MonitorMemoryUsage, null,
            TimeSpan.FromSeconds(MemoryMonitorIntervalSeconds),
            TimeSpan.FromSeconds(MemoryMonitorIntervalSeconds));

        // 🔧 注释冗余的启动日志 - 减少日志噪音
        // _logger.LogInformation("服务生命周期管理器已启动");
    }

    /// <summary>
    /// 跟踪服务注册信息
    /// </summary>
    public void TrackService(ServiceRegistrationInfo registration)
    {
        var lifecycleInfo = new ServiceLifecycleInfo
        {
            ServiceType = registration.ServiceType,
            ImplementationType = registration.ImplementationType,
            Lifetime = registration.Lifetime,
            RegisteredAt = registration.RegisteredAt,
            InstanceCount = 0,
            LastAccessedAt = DateTime.UtcNow
        };

        _serviceTracking.TryAdd(registration.ServiceType, lifecycleInfo);
        
        _logger.LogDebug("开始跟踪服务: {ServiceType} ({Lifetime})", 
            registration.ServiceType.Name, registration.Lifetime);
    }

    /// <summary>
    /// 跟踪服务实例创建
    /// </summary>
    public void TrackServiceInstance(Type serviceType, object instance)
    {
        if (_instanceTracking.Count >= MaxInstanceTrackingCount)
        {
            _logger.LogWarning("实例跟踪数量已达上限 {MaxCount}，跳过跟踪", MaxInstanceTrackingCount);
            return;
        }

        var instanceInfo = new ServiceInstanceInfo
        {
            ServiceType = serviceType,
            Instance = new WeakReference(instance),
            CreatedAt = DateTime.UtcNow,
            IsDisposed = false
        };

        _instanceTracking.TryAdd(instance, instanceInfo);

        // 更新服务统计信息
        if (_serviceTracking.TryGetValue(serviceType, out var lifecycleInfo))
        {
            Interlocked.Increment(ref lifecycleInfo.InstanceCount);
            lifecycleInfo.LastAccessedAt = DateTime.UtcNow;
        }

        _logger.LogTrace("跟踪服务实例: {ServiceType}", serviceType.Name);
    }

    /// <summary>
    /// 跟踪服务实例释放
    /// </summary>
    public void TrackServiceInstanceDisposal(object instance)
    {
        if (_instanceTracking.TryGetValue(instance, out var instanceInfo))
        {
            instanceInfo.IsDisposed = true;
            instanceInfo.DisposedAt = DateTime.UtcNow;

            // 更新服务统计信息
            if (_serviceTracking.TryGetValue(instanceInfo.ServiceType, out var lifecycleInfo))
            {
                Interlocked.Decrement(ref lifecycleInfo.InstanceCount);
            }

            _logger.LogTrace("服务实例已释放: {ServiceType}", instanceInfo.ServiceType.Name);
        }
    }

    /// <summary>
    /// 注册可释放资源进行跟踪管理
    /// </summary>
    /// <param name="disposable">可释放的资源</param>
    public void RegisterDisposable(IDisposable disposable)
    {
        if (disposable == null) return;
        
        TrackServiceInstance(disposable.GetType(), disposable);
        _logger.LogTrace("已注册可释放资源: {Type}", disposable.GetType().Name);
    }

    /// <summary>
    /// 获取服务生命周期统计信息
    /// </summary>
    public ServiceLifecycleStatistics GetStatistics()
    {
        var statistics = new ServiceLifecycleStatistics
        {
            TrackedServicesCount = _serviceTracking.Count,
            TrackedInstancesCount = _instanceTracking.Count,
            TotalInstancesCreated = _serviceTracking.Values.Sum(s => s.InstanceCount),
            MemoryUsageBytes = GC.GetTotalMemory(false),
            ActiveInstancesCount = _instanceTracking.Values.Count(i => !i.IsDisposed && i.Instance.IsAlive)
        };

        // 按生命周期分组统计
        var lifetimeGroups = _serviceTracking.Values.GroupBy(s => s.Lifetime);
        foreach (var group in lifetimeGroups)
        {
            switch (group.Key)
            {
                case ServiceLifetime.Singleton:
                    statistics.SingletonServicesCount = group.Count();
                    break;
                case ServiceLifetime.Scoped:
                    statistics.ScopedServicesCount = group.Count();
                    break;
                case ServiceLifetime.Transient:
                    statistics.TransientServicesCount = group.Count();
                    break;
            }
        }

        return statistics;
    }

    /// <summary>
    /// 检测内存泄漏
    /// </summary>
    public MemoryLeakReport DetectMemoryLeaks()
    {
        var report = new MemoryLeakReport();
        var currentTime = DateTime.UtcNow;

        foreach (var kvp in _instanceTracking)
        {
            var instanceInfo = kvp.Value;
            
            // 检查已释放但仍被引用的对象
            if (instanceInfo.IsDisposed && instanceInfo.Instance.IsAlive)
            {
                report.AddSuspiciousInstance(instanceInfo.ServiceType, 
                    "对象已标记为释放但仍被引用", instanceInfo.CreatedAt);
            }
            
            // 检查长时间未释放的瞬态服务
            if (_serviceTracking.TryGetValue(instanceInfo.ServiceType, out var lifecycleInfo) &&
                lifecycleInfo.Lifetime == ServiceLifetime.Transient &&
                !instanceInfo.IsDisposed &&
                currentTime - instanceInfo.CreatedAt > TimeSpan.FromHours(1))
            {
                report.AddSuspiciousInstance(instanceInfo.ServiceType,
                    "瞬态服务实例存活时间过长", instanceInfo.CreatedAt);
            }
        }

        // 检查单例服务的实例数量
        foreach (var lifecycleInfo in _serviceTracking.Values)
        {
            if (lifecycleInfo.Lifetime == ServiceLifetime.Singleton && lifecycleInfo.InstanceCount > 1)
            {
                report.AddSuspiciousService(lifecycleInfo.ServiceType,
                    $"单例服务存在多个实例: {lifecycleInfo.InstanceCount}");
            }
        }

        report.GeneratedAt = currentTime;
        return report;
    }

    /// <summary>
    /// 强制清理未使用的服务实例
    /// </summary>
    public CleanupResult ForceCleanup()
    {
        var result = new CleanupResult { StartedAt = DateTime.UtcNow };
        
        try
        {
            // 触发垃圾回收
            var memoryBefore = GC.GetTotalMemory(false);
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            var memoryAfter = GC.GetTotalMemory(false);
            
            result.MemoryFreedBytes = memoryBefore - memoryAfter;

            // 清理失效的弱引用
            var keysToRemove = new List<object>();
            foreach (var kvp in _instanceTracking)
            {
                if (!kvp.Value.Instance.IsAlive || kvp.Value.IsDisposed)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                _instanceTracking.TryRemove(key, out _);
                result.InstancesCleanedUp++;
            }

            result.IsSuccessful = true;
            _logger.LogInformation("强制清理完成，释放内存: {MemoryFreed}bytes, 清理实例: {InstancesCleaned}",
                result.MemoryFreedBytes, result.InstancesCleanedUp);
        }
        catch (Exception ex)
        {
            result.IsSuccessful = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, "强制清理过程中发生错误");
        }
        finally
        {
            result.CompletedAt = DateTime.UtcNow;
        }

        return result;
    }

    /// <summary>
    /// 优雅关闭所有跟踪的服务
    /// </summary>
    public async Task GracefulShutdownAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始优雅关闭服务生命周期管理器");

        try
        {
            // 停止定时器
            _cleanupTimer?.Dispose();
            _memoryMonitorTimer?.Dispose();

            // 释放所有可释放的实例
            var disposableTasks = new List<Task>();
            
            foreach (var instanceInfo in _instanceTracking.Values)
            {
                if (instanceInfo.Instance.IsAlive && 
                    instanceInfo.Instance.Target is IDisposable disposable)
                {
                    var task = Task.Run(() =>
                    {
                        try
                        {
                            disposable.Dispose();
                            TrackServiceInstanceDisposal(instanceInfo.Instance.Target!);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "释放服务实例时发生错误: {ServiceType}",
                                instanceInfo.ServiceType.Name);
                        }
                    }, cancellationToken);
                    
                    disposableTasks.Add(task);
                }
            }

            // 等待所有释放操作完成，最多等待30秒
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(TimeSpan.FromSeconds(30));
            
            await Task.WhenAll(disposableTasks).ConfigureAwait(false);

            _logger.LogInformation("优雅关闭完成，释放了 {Count} 个服务实例", disposableTasks.Count);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("优雅关闭操作被取消或超时");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "优雅关闭过程中发生错误");
        }
    }

    #region 私有方法

    private void PerformCleanup(object? state)
    {
        try
        {
            var result = ForceCleanup();
            if (result.IsSuccessful && result.InstancesCleanedUp > 0)
            {
                _logger.LogDebug("定期清理完成，清理实例: {Count}", result.InstancesCleanedUp);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "定期清理过程中发生错误");
        }
    }

    private void MonitorMemoryUsage(object? state)
    {
        try
        {
            var currentMemory = GC.GetTotalMemory(false);
            
            if (currentMemory > MemoryThresholdBytes)
            {
                _logger.LogWarning("内存使用量过高: {CurrentMemory}MB, 阈值: {Threshold}MB",
                    currentMemory / 1024 / 1024, MemoryThresholdBytes / 1024 / 1024);
                
                // 检测潜在的内存泄漏
                var leakReport = DetectMemoryLeaks();
                if (leakReport.HasSuspiciousInstances || leakReport.HasSuspiciousServices)
                {
                    _logger.LogWarning("检测到潜在内存泄漏，可疑实例: {SuspiciousInstances}, 可疑服务: {SuspiciousServices}",
                        leakReport.SuspiciousInstances.Count, leakReport.SuspiciousServices.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "内存监控过程中发生错误");
        }
    }

    #endregion

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            GracefulShutdownAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dispose过程中的优雅关闭失败");
        }
        finally
        {
            _cleanupTimer?.Dispose();
            _memoryMonitorTimer?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// 服务生命周期信息
/// </summary>
public class ServiceLifecycleInfo
{
    public Type ServiceType { get; set; } = null!;
    public Type ImplementationType { get; set; } = null!;
    public ServiceLifetime Lifetime { get; set; }
    public DateTime RegisteredAt { get; set; }
    public long InstanceCount;
    public DateTime LastAccessedAt { get; set; }
}

/// <summary>
/// 服务实例信息
/// </summary>
public class ServiceInstanceInfo
{
    public Type ServiceType { get; set; } = null!;
    public WeakReference Instance { get; set; } = null!;
    public DateTime CreatedAt { get; set; }
    public bool IsDisposed { get; set; }
    public DateTime? DisposedAt { get; set; }
}

/// <summary>
/// 服务生命周期统计信息
/// </summary>
public class ServiceLifecycleStatistics
{
    public int TrackedServicesCount { get; set; }
    public int TrackedInstancesCount { get; set; }
    public long TotalInstancesCreated { get; set; }
    public long MemoryUsageBytes { get; set; }
    public int ActiveInstancesCount { get; set; }
    public int SingletonServicesCount { get; set; }
    public int ScopedServicesCount { get; set; }
    public int TransientServicesCount { get; set; }
}

/// <summary>
/// 内存泄漏报告
/// </summary>
public class MemoryLeakReport
{
    public DateTime GeneratedAt { get; set; }
    public List<SuspiciousInstance> SuspiciousInstances { get; } = new();
    public List<SuspiciousService> SuspiciousServices { get; } = new();
    
    public bool HasSuspiciousInstances => SuspiciousInstances.Any();
    public bool HasSuspiciousServices => SuspiciousServices.Any();

    public void AddSuspiciousInstance(Type serviceType, string reason, DateTime createdAt)
    {
        SuspiciousInstances.Add(new SuspiciousInstance
        {
            ServiceType = serviceType,
            Reason = reason,
            CreatedAt = createdAt
        });
    }

    public void AddSuspiciousService(Type serviceType, string reason)
    {
        SuspiciousServices.Add(new SuspiciousService
        {
            ServiceType = serviceType,
            Reason = reason
        });
    }
}

/// <summary>
/// 可疑实例信息
/// </summary>
public class SuspiciousInstance
{
    public Type ServiceType { get; set; } = null!;
    public string Reason { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 可疑服务信息
/// </summary>
public class SuspiciousService
{
    public Type ServiceType { get; set; } = null!;
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 清理结果
/// </summary>
public class CleanupResult
{
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public bool IsSuccessful { get; set; }
    public long MemoryFreedBytes { get; set; }
    public int InstancesCleanedUp { get; set; }
    public string? ErrorMessage { get; set; }
    
    public TimeSpan Duration => CompletedAt - StartedAt;
}