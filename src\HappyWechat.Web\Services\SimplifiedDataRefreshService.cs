using Microsoft.Extensions.Logging;
using HappyWechat.Web.Services.Interfaces;
using HappyWechat.Web.Services.Models;
using HappyWechat.Infrastructure.Integration;
using HappyWechat.Infrastructure.Notifications;
using HappyWechat.Infrastructure.Caching.Interfaces;
using System.Collections.Concurrent;

namespace HappyWechat.Web.Services;

/// <summary>
/// 简化数据刷新服务实现
/// 基于统一Redis架构，替代Actor系统的数据刷新功能
/// </summary>
public class SimplifiedDataRefreshService : ISimplifiedDataRefreshService
{
    private readonly ILogger<SimplifiedDataRefreshService> _logger;
    private readonly IUnifiedArchitectureIntegrationService _integrationService;
    private readonly IUnifiedRedisNotificationService _notificationService;
    private readonly IUnifiedRedisCacheService _cacheService;
    private readonly ConcurrentDictionary<Guid, DateTime> _lastRefreshTimes;
    private readonly SemaphoreSlim _refreshSemaphore;
    private readonly RefreshOperationTracker _operationTracker;

    public SimplifiedDataRefreshService(
        ILogger<SimplifiedDataRefreshService> logger,
        IUnifiedArchitectureIntegrationService integrationService,
        IUnifiedRedisNotificationService notificationService,
        IUnifiedRedisCacheService cacheService)
    {
        _logger = logger;
        _integrationService = integrationService;
        _notificationService = notificationService;
        _cacheService = cacheService;
        _lastRefreshTimes = new ConcurrentDictionary<Guid, DateTime>();
        _refreshSemaphore = new SemaphoreSlim(5, 5); // 最多5个并发刷新操作
        _operationTracker = new RefreshOperationTracker();
    }

    public async Task<bool> RefreshContactsAsync(Guid wxManagerId)
    {
        return await RefreshDataInternalAsync(wxManagerId, "contact", async () =>
        {
            // 🔧 注释冗余的开始刷新日志 - 减少日志噪音
            // _logger.LogInformation("开始刷新联系人数据 - WxManagerId: {WxManagerId}", wxManagerId);

            // 清理联系人相关缓存
            await _integrationService.ClearAccountCacheAsync(wxManagerId);

            // 发送缓存失效通知
            await _notificationService.PublishCacheInvalidationByPatternAsync($"contact:*:{wxManagerId}");

            // 发送UI刷新通知
            await _notificationService.PublishUIRefreshAsync(wxManagerId, "contact");

            // 🔧 注释冗余的刷新完成日志 - 减少日志噪音
            // _logger.LogInformation("联系人数据刷新完成 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        });
    }

    /// <summary>
    /// 强制刷新联系人数据（绕过频率限制）- 用于批量操作后的数据刷新
    /// </summary>
    public async Task<bool> ForceRefreshContactsAsync(Guid wxManagerId, string reason = "批量操作")
    {
        _logger.LogInformation("🔄 强制刷新联系人数据 - WxManagerId: {WxManagerId}, 原因: {Reason}", wxManagerId, reason);

        // 开始跟踪强制刷新操作
        if (!_operationTracker.StartOperation(wxManagerId, "ForceRefresh", reason))
        {
            _logger.LogWarning("⚠️ 强制刷新操作已在进行中，跳过重复操作 - WxManagerId: {WxManagerId}", wxManagerId);
            return true; // 返回成功，因为已有操作在进行
        }

        await _refreshSemaphore.WaitAsync();
        try
        {
            // 清理联系人相关缓存
            await _integrationService.ClearAccountCacheAsync(wxManagerId);

            // 发送缓存失效通知
            await _notificationService.PublishCacheInvalidationByPatternAsync($"contact:*:{wxManagerId}");

            // 发送UI刷新通知
            await _notificationService.PublishUIRefreshAsync(wxManagerId, "contact");

            // 强制更新最后刷新时间
            _lastRefreshTimes.AddOrUpdate(wxManagerId, DateTime.UtcNow, (key, oldValue) => DateTime.UtcNow);

            // 发送刷新完成通知
            await _notificationService.PublishRealtimeNotificationAsync(
                $"wxmanager_{wxManagerId}",
                "DataRefreshCompleted",
                new { WxManagerId = wxManagerId, DataType = "contact", Reason = reason, Timestamp = DateTime.UtcNow });

            _logger.LogInformation("✅ 强制刷新联系人数据完成 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 强制刷新联系人数据失败 - WxManagerId: {WxManagerId}", wxManagerId);

            // 发送刷新失败通知
            await _notificationService.PublishRealtimeNotificationAsync(
                $"wxmanager_{wxManagerId}",
                "DataRefreshFailed",
                new { WxManagerId = wxManagerId, DataType = "contact", Error = ex.Message, Timestamp = DateTime.UtcNow });

            return false;
        }
        finally
        {
            _refreshSemaphore.Release();
            // 完成操作跟踪
            _operationTracker.CompleteOperation(wxManagerId, "ForceRefresh");
        }
    }

    public async Task<bool> RefreshGroupsAsync(Guid wxManagerId)
    {
        return await RefreshDataInternalAsync(wxManagerId, "group", async () =>
        {
            _logger.LogInformation("开始刷新群组数据 - WxManagerId: {WxManagerId}", wxManagerId);
            
            // 清理群组相关缓存
            await _integrationService.ClearAccountCacheAsync(wxManagerId);
            
            // 发送缓存失效通知
            await _notificationService.PublishCacheInvalidationByPatternAsync($"group:*:{wxManagerId}");
            
            // 发送UI刷新通知
            await _notificationService.PublishUIRefreshAsync(wxManagerId, "group");
            
            _logger.LogInformation("群组数据刷新完成 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        });
    }

    public async Task<bool> RefreshAiConfigsAsync(Guid wxManagerId)
    {
        return await RefreshDataInternalAsync(wxManagerId, "aiconfig", async () =>
        {
            _logger.LogInformation("开始刷新AI配置数据 - WxManagerId: {WxManagerId}", wxManagerId);
            
            // 清理AI配置相关缓存
            await _integrationService.ClearAccountCacheAsync(wxManagerId);
            
            // 发送缓存失效通知
            await _notificationService.PublishCacheInvalidationByPatternAsync($"aiconfig:*:{wxManagerId}");
            
            // 发送UI刷新通知
            await _notificationService.PublishUIRefreshAsync(wxManagerId, "aiconfig");
            
            _logger.LogInformation("AI配置数据刷新完成 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        });
    }

    public async Task<bool> RefreshAllDataAsync(Guid wxManagerId)
    {
        return await RefreshDataInternalAsync(wxManagerId, "all", async () =>
        {
            _logger.LogInformation("开始刷新所有数据 - WxManagerId: {WxManagerId}", wxManagerId);
            
            // 清理所有相关缓存
            await _integrationService.ClearAccountCacheAsync(wxManagerId);
            
            // 发送全局缓存失效通知
            await _notificationService.PublishCacheInvalidationByPatternAsync($"*:{wxManagerId}");
            
            // 发送多个UI刷新通知
            var refreshTasks = new[]
            {
                _notificationService.PublishUIRefreshAsync(wxManagerId, "contact"),
                _notificationService.PublishUIRefreshAsync(wxManagerId, "group"),
                _notificationService.PublishUIRefreshAsync(wxManagerId, "aiconfig")
            };
            
            await Task.WhenAll(refreshTasks);
            
            _logger.LogInformation("所有数据刷新完成 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        });
    }

    public async Task<string> GetRefreshStatusAsync(Guid wxManagerId)
    {
        try
        {
            if (_lastRefreshTimes.TryGetValue(wxManagerId, out var lastRefreshTime))
            {
                var timeSinceRefresh = DateTime.UtcNow - lastRefreshTime;
                
                if (timeSinceRefresh.TotalMinutes < 1)
                {
                    return "最近刚刷新";
                }
                else if (timeSinceRefresh.TotalMinutes < 60)
                {
                    return $"{(int)timeSinceRefresh.TotalMinutes}分钟前刷新";
                }
                else if (timeSinceRefresh.TotalHours < 24)
                {
                    return $"{(int)timeSinceRefresh.TotalHours}小时前刷新";
                }
                else
                {
                    return $"{(int)timeSinceRefresh.TotalDays}天前刷新";
                }
            }
            
            return "未刷新";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取刷新状态失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return "状态未知";
        }
    }

    public async Task<bool> RefreshContactDataAsync(Guid wxManagerId)
    {
        // 这是一个别名方法，调用RefreshContactsAsync
        return await RefreshContactsAsync(wxManagerId);
    }

    /// <summary>
    /// 组件级刷新联系人数据（检查批量操作冲突）
    /// </summary>
    public async Task<bool> ComponentRefreshContactsAsync(Guid wxManagerId)
    {
        // 检查是否有批量操作正在进行
        if (_operationTracker.IsBatchOperationActive(wxManagerId))
        {
            _logger.LogWarning("批量操作正在进行中，跳过组件级刷新 - WxManagerId: {WxManagerId}", wxManagerId);
            return false;
        }

        // 调用普通刷新
        return await RefreshContactsAsync(wxManagerId);
    }

    /// <summary>
    /// 内部刷新数据方法，提供统一的错误处理和并发控制
    /// </summary>
    private async Task<bool> RefreshDataInternalAsync(Guid wxManagerId, string dataType, Func<Task<bool>> refreshAction)
    {
        // 防止过于频繁的刷新
        if (IsRefreshTooFrequent(wxManagerId))
        {
            _logger.LogWarning("刷新过于频繁，跳过本次刷新 - WxManagerId: {WxManagerId}, DataType: {DataType}",
                wxManagerId, dataType);
            return false;
        }

        await _refreshSemaphore.WaitAsync();
        try
        {
            var result = await refreshAction();
            
            if (result)
            {
                // 更新最后刷新时间
                _lastRefreshTimes.AddOrUpdate(wxManagerId, DateTime.UtcNow, (key, oldValue) => DateTime.UtcNow);
                
                // 发送刷新完成通知
                await _notificationService.PublishRealtimeNotificationAsync(
                    $"wxmanager_{wxManagerId}", 
                    "DataRefreshCompleted", 
                    new { WxManagerId = wxManagerId, DataType = dataType, Timestamp = DateTime.UtcNow });
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据刷新失败 - WxManagerId: {WxManagerId}, DataType: {DataType}", 
                wxManagerId, dataType);
            
            // 发送刷新失败通知
            await _notificationService.PublishRealtimeNotificationAsync(
                $"wxmanager_{wxManagerId}", 
                "DataRefreshFailed", 
                new { WxManagerId = wxManagerId, DataType = dataType, Error = ex.Message, Timestamp = DateTime.UtcNow });
            
            return false;
        }
        finally
        {
            _refreshSemaphore.Release();
        }
    }

    /// <summary>
    /// 检查刷新是否过于频繁
    /// </summary>
    private bool IsRefreshTooFrequent(Guid wxManagerId)
    {
        if (_lastRefreshTimes.TryGetValue(wxManagerId, out var lastRefreshTime))
        {
            var timeSinceRefresh = DateTime.UtcNow - lastRefreshTime;
            return timeSinceRefresh.TotalSeconds < 10; // 10秒内不允许重复刷新
        }
        
        return false;
    }

    /// <summary>
    /// 批量刷新多个账户的数据
    /// </summary>
    public async Task<Dictionary<Guid, bool>> RefreshMultipleAccountsAsync(List<Guid> wxManagerIds, string dataType)
    {
        var results = new Dictionary<Guid, bool>();
        
        var refreshTasks = wxManagerIds.Select(async wxManagerId =>
        {
            var result = dataType.ToLower() switch
            {
                "contact" => await RefreshContactsAsync(wxManagerId),
                "group" => await RefreshGroupsAsync(wxManagerId),
                "aiconfig" => await RefreshAiConfigsAsync(wxManagerId),
                "all" => await RefreshAllDataAsync(wxManagerId),
                _ => false
            };
            
            return new { WxManagerId = wxManagerId, Result = result };
        });

        var completedTasks = await Task.WhenAll(refreshTasks);
        
        foreach (var task in completedTasks)
        {
            results[task.WxManagerId] = task.Result;
        }

        _logger.LogInformation("批量刷新完成 - DataType: {DataType}, 成功: {SuccessCount}, 失败: {FailCount}", 
            dataType, results.Values.Count(r => r), results.Values.Count(r => !r));

        return results;
    }

    /// <summary>
    /// 清理过期的刷新时间记录
    /// </summary>
    public void CleanupExpiredRefreshTimes()
    {
        var expiredKeys = _lastRefreshTimes
            .Where(kvp => DateTime.UtcNow - kvp.Value > TimeSpan.FromDays(1))
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in expiredKeys)
        {
            _lastRefreshTimes.TryRemove(key, out _);
        }

        if (expiredKeys.Any())
        {
            _logger.LogDebug("清理了 {Count} 个过期的刷新时间记录", expiredKeys.Count);
        }
    }
}
