using HappyWechat.Web.Services.Interfaces;
using HappyWechat.Application.DTOs.Responses;
using ContactAiConfigDto = HappyWechat.Application.DTOs.AiConfig.ContactAiConfigDto;
using GroupAiConfigDto = HappyWechat.Application.DTOs.AiConfig.GroupAiConfigDto;
using HappyWechat.Web.Models;
using HappyWechat.Infrastructure.Integration;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Web.Services;

/// <summary>
/// 统一数据管理器实现 - 整合所有数据管理功能
/// 支持API控制器和Blazor组件两种使用场景，通过可选的UI通知服务提供UI功能
/// </summary>
public class UnifiedDataManager : IUnifiedDataManager
{
    private readonly IUnifiedArchitectureIntegrationService _integrationService;
    private readonly ILogger<UnifiedDataManager> _logger;
    private readonly IUINotificationService? _uiNotificationService;

    public UnifiedDataManager(
        IUnifiedArchitectureIntegrationService integrationService,
        ILogger<UnifiedDataManager> logger,
        IUINotificationService? uiNotificationService = null)
    {
        _integrationService = integrationService;
        _logger = logger;
        _uiNotificationService = uiNotificationService;
    }

    public async Task<DataBundle<WxContactDto, ContactAiConfigDto>?> GetContactDataBundleAsync(
        Guid wxManagerId, 
        bool forceRefresh = false)
    {
        try
        {
            _logger.LogDebug("获取联系人数据包 - WxManagerId: {WxManagerId}, ForceRefresh: {ForceRefresh}", 
                wxManagerId, forceRefresh);

            if (forceRefresh)
            {
                await _integrationService.ClearAccountCacheAsync(wxManagerId);
            }

            // 获取联系人数据
            var contacts = await _integrationService.GetContactDataAsync<WxContactDto>(wxManagerId, forceRefresh);
            
            // 获取联系人AI配置
            var aiConfigs = await _integrationService.GetContactAiConfigsAsync<ContactAiConfigDto>(wxManagerId, forceRefresh);

            var dataBundle = new DataBundle<WxContactDto, ContactAiConfigDto>
            {
                Data = contacts,
                AiConfigs = aiConfigs
            };

            _logger.LogDebug("联系人数据包获取成功 - 联系人数量: {ContactCount}, AI配置数量: {AiConfigCount}", 
                contacts.Count, aiConfigs.Count);

            return dataBundle;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取联系人数据包失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    public async Task<DataBundle<WxGroupVo, GroupAiConfigDto>?> GetGroupDataBundleAsync(
        Guid wxManagerId, 
        bool forceRefresh = false)
    {
        try
        {
            _logger.LogDebug("获取群组数据包 - WxManagerId: {WxManagerId}, ForceRefresh: {ForceRefresh}", 
                wxManagerId, forceRefresh);

            if (forceRefresh)
            {
                await _integrationService.ClearAccountCacheAsync(wxManagerId);
            }

            // 获取群组数据
            var groups = await _integrationService.GetGroupDataAsync<WxGroupVo>(wxManagerId, forceRefresh);
            
            // 获取群组AI配置
            var aiConfigs = await _integrationService.GetGroupAiConfigsAsync<GroupAiConfigDto>(wxManagerId, forceRefresh);

            var dataBundle = new DataBundle<WxGroupVo, GroupAiConfigDto>
            {
                Data = groups,
                AiConfigs = aiConfigs
            };

            _logger.LogDebug("群组数据包获取成功 - 群组数量: {GroupCount}, AI配置数量: {AiConfigCount}", 
                groups.Count, aiConfigs.Count);

            return dataBundle;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组数据包失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    public async Task<bool> RefreshDataAsync(Guid wxManagerId, string dataType)
    {
        try
        {
            _logger.LogInformation("刷新数据 - WxManagerId: {WxManagerId}, DataType: {DataType}", 
                wxManagerId, dataType);

            // 清理缓存
            await _integrationService.ClearAccountCacheAsync(wxManagerId);

            // 根据数据类型刷新对应数据
            switch (dataType.ToLower())
            {
                case "contact":
                    var contactBundle = await GetContactDataBundleAsync(wxManagerId, true);
                    return contactBundle != null;
                    
                case "group":
                    var groupBundle = await GetGroupDataBundleAsync(wxManagerId, true);
                    return groupBundle != null;
                    
                default:
                    _logger.LogWarning("未知的数据类型 - DataType: {DataType}", dataType);
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新数据失败 - WxManagerId: {WxManagerId}, DataType: {DataType}", 
                wxManagerId, dataType);
            return false;
        }
    }

    public async Task<bool> ClearCacheAsync(Guid wxManagerId)
    {
        try
        {
            _logger.LogInformation("清理缓存 - WxManagerId: {WxManagerId}", wxManagerId);
            
            await _integrationService.ClearAccountCacheAsync(wxManagerId);
            
            _logger.LogInformation("缓存清理成功 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理缓存失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return false;
        }
    }

    public async Task<bool> InvalidateCacheAsync(Guid wxManagerId)
    {
        try
        {
            // 🔧 注释冗余的缓存失效开始日志 - 减少日志噪音
            // _logger.LogInformation("使缓存失效 - WxManagerId: {WxManagerId}", wxManagerId);

            // 使用清理缓存的方式来使缓存失效
            await _integrationService.ClearAccountCacheAsync(wxManagerId);

            // 🔧 注释冗余的缓存失效成功日志 - 减少日志噪音
            // _logger.LogInformation("缓存失效成功 - WxManagerId: {WxManagerId}", wxManagerId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使缓存失效失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return false;
        }
    }

    public async Task<T?> GetDataBundleAsync<T>(Guid wxManagerId, bool forceRefresh = false) where T : class
    {
        try
        {
            _logger.LogDebug("获取泛型数据包 - WxManagerId: {WxManagerId}, Type: {Type}, ForceRefresh: {ForceRefresh}",
                wxManagerId, typeof(T).Name, forceRefresh);

            // 根据类型返回对应的数据包
            if (typeof(T) == typeof(DataBundle<WxContactDto, ContactAiConfigDto>))
            {
                var contactBundle = await GetContactDataBundleAsync(wxManagerId, forceRefresh);
                return contactBundle as T;
            }
            else if (typeof(T) == typeof(DataBundle<WxGroupVo, GroupAiConfigDto>))
            {
                var groupBundle = await GetGroupDataBundleAsync(wxManagerId, forceRefresh);
                return groupBundle as T;
            }
            else
            {
                _logger.LogWarning("不支持的数据包类型 - Type: {Type}", typeof(T).Name);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取泛型数据包失败 - WxManagerId: {WxManagerId}, Type: {Type}",
                wxManagerId, typeof(T).Name);
            return null;
        }
    }

    public async Task<Dictionary<string, bool>> BatchRefreshDataAsync(Guid wxManagerId, params string[] dataTypes)
    {
        var results = new Dictionary<string, bool>();

        try
        {
            _logger.LogInformation("批量刷新数据 - WxManagerId: {WxManagerId}, DataTypes: {DataTypes}",
                wxManagerId, string.Join(", ", dataTypes));

            foreach (var dataType in dataTypes)
            {
                try
                {
                    var result = await RefreshDataAsync(wxManagerId, dataType);
                    results[dataType] = result;

                    _logger.LogDebug("数据类型 {DataType} 刷新结果: {Result}", dataType, result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "刷新数据类型 {DataType} 失败", dataType);
                    results[dataType] = false;
                }
            }

            var successCount = results.Values.Count(r => r);
            _logger.LogInformation("批量刷新完成 - 成功: {SuccessCount}/{TotalCount}",
                successCount, dataTypes.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量刷新数据失败 - WxManagerId: {WxManagerId}", wxManagerId);

            // 确保所有数据类型都有结果
            foreach (var dataType in dataTypes)
            {
                if (!results.ContainsKey(dataType))
                {
                    results[dataType] = false;
                }
            }
        }

        return results;
    }

    // ========================================
    // 🎯 UI通知功能实现 - 支持Blazor组件
    // ========================================

    public async Task<DataBundle<WxContactDto, ContactAiConfigDto>?> GetContactDataBundleWithNotificationAsync(
        Guid wxManagerId,
        bool forceRefresh = false,
        bool showNotification = true)
    {
        try
        {
            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                _uiNotificationService.ShowLoading("正在加载联系人数据...");
            }

            var result = await GetContactDataBundleAsync(wxManagerId, forceRefresh);

            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                if (result != null)
                {
                    _uiNotificationService.ShowSuccess("联系人数据加载成功");
                }
                else
                {
                    _uiNotificationService.ShowError("联系人数据加载失败");
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取联系人数据包失败（带通知） - WxManagerId: {WxManagerId}", wxManagerId);

            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                _uiNotificationService.ShowError("联系人数据加载失败");
            }

            return null;
        }
    }

    public async Task<DataBundle<WxGroupVo, GroupAiConfigDto>?> GetGroupDataBundleWithNotificationAsync(
        Guid wxManagerId,
        bool forceRefresh = false,
        bool showNotification = true)
    {
        try
        {
            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                _uiNotificationService.ShowLoading("正在加载群组数据...");
            }

            var result = await GetGroupDataBundleAsync(wxManagerId, forceRefresh);

            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                if (result != null)
                {
                    _uiNotificationService.ShowSuccess("群组数据加载成功");
                }
                else
                {
                    _uiNotificationService.ShowError("群组数据加载失败");
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组数据包失败（带通知） - WxManagerId: {WxManagerId}", wxManagerId);

            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                _uiNotificationService.ShowError("群组数据加载失败");
            }

            return null;
        }
    }

    public async Task<bool> ClearCacheWithNotificationAsync(Guid wxManagerId, bool showNotification = true)
    {
        try
        {
            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                _uiNotificationService.ShowLoading("正在清理缓存...");
            }

            var result = await ClearCacheAsync(wxManagerId);

            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                if (result)
                {
                    _uiNotificationService.ShowSuccess("缓存清理成功");
                }
                else
                {
                    _uiNotificationService.ShowError("缓存清理失败");
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理缓存失败（带通知） - WxManagerId: {WxManagerId}", wxManagerId);

            if (showNotification && _uiNotificationService?.IsAvailable == true)
            {
                _uiNotificationService.ShowError("缓存清理失败");
            }

            return false;
        }
    }
}
