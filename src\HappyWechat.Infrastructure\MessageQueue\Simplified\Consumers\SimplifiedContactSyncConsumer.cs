using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests.Friends;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Friends;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers;

/// <summary>
/// 简化联系人同步消费者 - 处理ContactSyncMessage消息
/// 实现批量和单个联系人同步，支持随机延时
/// </summary>
public class SimplifiedContactSyncConsumer : SimplifiedQueueConsumerBase<ContactSyncMessage>
{
    public SimplifiedContactSyncConsumer(
        IServiceProvider serviceProvider,
        ILogger<SimplifiedContactSyncConsumer> logger)
        : base(serviceProvider, logger, "contact_sync", batchSize: 10, maxConcurrency: 2, delayMs: 1000)
    {
    }

    protected override async Task<bool> ProcessMessageAsync(SimplifiedQueueMessage<ContactSyncMessage> message, CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();

            var syncMessage = message.Data;

            // 🔧 注释冗余的消息验证详情日志 - 减少日志噪音
            // _logger.LogInformation("🔍 消息验证详情 - MessageId: {MessageId}, WxManagerId: {WxManagerId}, SyncSessionId: {SyncSessionId}, ListType: {ListType}, ContactIds: {ContactIds}, BatchIndex: {BatchIndex}, TotalBatches: {TotalBatches}, BatchSize: {BatchSize}",
            //     message.Id, syncMessage.WxManagerId, syncMessage.SyncSessionId, syncMessage.ListType,
            //     string.Join(",", syncMessage.ContactIds ?? new List<string>()), syncMessage.BatchIndex, syncMessage.TotalBatches, syncMessage.BatchSize);

            if (!syncMessage.IsValid())
            {
                _logger.LogError("❌ 无效的联系人同步消息 - MessageId: {MessageId}, WxManagerId: {WxManagerId}, 详细信息: WxManagerId={WxManagerId}, SyncSessionId='{SyncSessionId}', ContactIds={ContactIds}, BatchIndex={BatchIndex}, TotalBatches={TotalBatches}, BatchSize={BatchSize}",
                    message.Id, message.WxManagerId, syncMessage.WxManagerId, syncMessage.SyncSessionId,
                    syncMessage.ContactIds?.Count ?? 0, syncMessage.BatchIndex, syncMessage.TotalBatches, syncMessage.BatchSize);
                return false;
            }

            // 🔧 注释冗余的开始处理日志 - 减少日志噪音
            // _logger.LogInformation("📞 开始处理联系人同步 - MessageId: {MessageId}, WxManagerId: {WxManagerId}, {BatchDescription}",
            //     message.Id, message.WxManagerId, syncMessage.BatchDescription);

            // 执行随机延时
            var delayService = scope.ServiceProvider.GetRequiredService<ContactSyncDelayService>();
            var actualDelay = syncMessage.DelayConfig.GetActualDelay();
            _logger.LogDebug("执行随机延时: {Delay}ms", actualDelay);
            await Task.Delay(actualDelay, cancellationToken);

            // 根据联系人类型处理
            bool success = syncMessage.ListType switch
            {
                WxContactListType.Friends => await ProcessFriendsContactsAsync(syncMessage, scope.ServiceProvider, cancellationToken),
                WxContactListType.Enterprise => await ProcessEnterpriseContactsAsync(syncMessage, scope.ServiceProvider, cancellationToken),
                _ => throw new NotSupportedException($"不支持的联系人类型: {syncMessage.ListType}")
            };

            // 更新进度
            var progressTracker = scope.ServiceProvider.GetRequiredService<ContactSyncProgressTracker>();
            var successCount = success ? syncMessage.BatchSize : 0;
            var failedCount = success ? 0 : syncMessage.BatchSize;

            await progressTracker.UpdateBatchProgressAsync(
                syncMessage.WxManagerId,
                syncMessage.ListType,
                syncMessage.BatchIndex,
                syncMessage.BatchSize,
                successCount,
                failedCount);

            if (success)
            {
                // 🔧 提升为Warning级别 - 重要的同步完成日志
                _logger.LogWarning("✅ 联系人同步批次完成 - WxManagerId: {WxManagerId}, {BatchDescription}, 成功: {Success}个",
                    message.WxManagerId, syncMessage.BatchDescription, successCount);
            }
            else
            {
                _logger.LogError("❌ 联系人同步批次失败 - WxManagerId: {WxManagerId}, {BatchDescription}",
                    message.WxManagerId, syncMessage.BatchDescription);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 联系人同步处理异常 - MessageId: {MessageId}, WxManagerId: {WxManagerId}",
                message.Id, message.WxManagerId);

            // 更新进度为失败
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var progressTracker = scope.ServiceProvider.GetRequiredService<ContactSyncProgressTracker>();
                await progressTracker.UpdateBatchProgressAsync(
                    message.WxManagerId,
                    message.Data.ListType,
                    message.Data.BatchIndex,
                    message.Data.BatchSize,
                    0, // 成功数
                    message.Data.BatchSize); // 失败数
            }
            catch (Exception progressEx)
            {
                _logger.LogError(progressEx, "更新进度失败");
            }

            return false;
        }
    }

    /// <summary>
    /// 处理个人联系人批次（使用/getContact接口，批量处理）
    /// </summary>
    private async Task<bool> ProcessFriendsContactsAsync(ContactSyncMessage syncMessage, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var eYunWrapper = serviceProvider.GetRequiredService<IEYunWrapper>();
            var contactMappingService = serviceProvider.GetRequiredService<IContactMappingService>();
            var wxManagerRepository = serviceProvider.GetRequiredService<IWxManagerRepository>();

            // 获取微信管理器信息
            var wxManager = await wxManagerRepository.GetByIdAsync(syncMessage.WxManagerId);
            if (wxManager == null || string.IsNullOrEmpty(wxManager.WId))
            {
                _logger.LogError("❌ 未找到有效的微信管理器 - WxManagerId: {WxManagerId}", syncMessage.WxManagerId);
                return false;
            }

            // 准备批量请求
            var contactIds = string.Join(",", syncMessage.ContactIds);
            var request = new EYunGetContactRequest
            {
                WId = wxManager.WId,
                WcId = contactIds
            };

            _logger.LogDebug("📞 调用/getContact接口 - WId: {WId}, ContactIds: {ContactIds}",
                wxManager.WId, contactIds);

            // 调用EYun API获取联系人详情
            var contactDetails = await eYunWrapper.GetContactAsync(request);

            if (contactDetails == null || !contactDetails.Any())
            {
                _logger.LogWarning("⚠️ /getContact接口返回空结果 - WxManagerId: {WxManagerId}, ContactIds: {ContactIds}",
                    syncMessage.WxManagerId, contactIds);
                return false;
            }

            // 🔧 注释冗余的获取详情日志 - 减少日志噪音
            // _logger.LogInformation("✅ 获取到个人联系人详情 - 数量: {Count}, 请求数量: {RequestCount}",
            //     contactDetails.Count, syncMessage.ContactIds.Count);

            // 保存联系人详情到数据库
            var successCount = 0;
            foreach (var contactDetail in contactDetails)
            {
                try
                {
                    var success = await SavePersonalContactDetailAsync(syncMessage.WxManagerId, contactDetail, serviceProvider, cancellationToken);
                    if (success)
                    {
                        successCount++;
                        _logger.LogDebug("✅ 个人联系人保存成功 - UserName: {UserName}, NickName: {NickName}",
                            contactDetail.UserName, contactDetail.NickName);
                    }
                    else
                    {
                        _logger.LogWarning("⚠️ 个人联系人保存失败 - UserName: {UserName}", contactDetail.UserName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ 保存个人联系人异常 - UserName: {UserName}", contactDetail.UserName);
                }
            }

            var isSuccess = successCount > 0;
            // 🔧 注释冗余的批次处理完成日志 - 减少日志噪音
            // _logger.LogInformation("📊 个人联系人批次处理完成 - 成功: {Success}/{Total}", successCount, contactDetails.Count);

            return isSuccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理个人联系人批次异常 - WxManagerId: {WxManagerId}, BatchIndex: {BatchIndex}",
                syncMessage.WxManagerId, syncMessage.BatchIndex);
            return false;
        }
    }

    /// <summary>
    /// 处理企业联系人（使用/getOpenImContact接口，单个处理）
    /// </summary>
    private async Task<bool> ProcessEnterpriseContactsAsync(ContactSyncMessage syncMessage, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var eYunFriendWrapper = serviceProvider.GetRequiredService<IEYunFriendWrapper>();
            var contactMappingService = serviceProvider.GetRequiredService<IContactMappingService>();
            var wxManagerRepository = serviceProvider.GetRequiredService<IWxManagerRepository>();

            // 获取微信管理器信息
            var wxManager = await wxManagerRepository.GetByIdAsync(syncMessage.WxManagerId);
            if (wxManager == null || string.IsNullOrEmpty(wxManager.WId))
            {
                _logger.LogError("❌ 未找到有效的微信管理器 - WxManagerId: {WxManagerId}", syncMessage.WxManagerId);
                return false;
            }

            // 企业联系人只能一个一个处理
            var contactId = syncMessage.ContactIds.FirstOrDefault();
            if (string.IsNullOrEmpty(contactId))
            {
                _logger.LogError("❌ 企业联系人ID为空 - WxManagerId: {WxManagerId}", syncMessage.WxManagerId);
                return false;
            }

            var request = new EYunGetOpenImContactRequest
            {
                WId = wxManager.WId,
                WcId = contactId
            };

            _logger.LogDebug("📞 调用/getOpenImContact接口 - WId: {WId}, ContactId: {ContactId}",
                wxManager.WId, contactId);

            // 调用EYun API获取企业联系人详情
            var contactDetail = await eYunFriendWrapper.GetOpenImContactAsync(request);

            if (contactDetail == null)
            {
                _logger.LogWarning("⚠️ /getOpenImContact接口返回空结果 - WxManagerId: {WxManagerId}, ContactId: {ContactId}",
                    syncMessage.WxManagerId, contactId);
                return false;
            }

            // 🔧 注释冗余的获取企业联系人详情日志 - 减少日志噪音
            // _logger.LogInformation("✅ 获取到企业联系人详情 - UserName: {UserName}, NickName: {NickName}",
            //     contactDetail.UserName, contactDetail.NickName);

            // 保存企业联系人详情到数据库
            var success = await SaveEnterpriseContactDetailAsync(syncMessage.WxManagerId, contactDetail, serviceProvider, cancellationToken);

            if (success)
            {
                _logger.LogDebug("✅ 企业联系人保存成功 - UserName: {UserName}, NickName: {NickName}",
                    contactDetail.UserName, contactDetail.NickName);
            }
            else
            {
                _logger.LogWarning("⚠️ 企业联系人保存失败 - UserName: {UserName}", contactDetail.UserName);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 处理企业联系人异常 - WxManagerId: {WxManagerId}, ContactId: {ContactId}",
                syncMessage.WxManagerId, syncMessage.ContactIds.FirstOrDefault());
            return false;
        }
    }

    /// <summary>
    /// 保存个人联系人详情到数据库
    /// </summary>
    private async Task<bool> SavePersonalContactDetailAsync(Guid wxManagerId, EYunGetContactData contactData, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var contactMappingService = serviceProvider.GetRequiredService<IContactMappingService>();
            var dbContextFactory = serviceProvider.GetRequiredService<Microsoft.EntityFrameworkCore.IDbContextFactory<HappyWechat.Infrastructure.Identity.Repositories.ApplicationDbContext>>();

            // 映射联系人数据
            var contactEntity = await contactMappingService.MapPersonalContactAsync(contactData, wxManagerId, WxContactType.Contact);

            // 保存到数据库
            using var dbContext = await dbContextFactory.CreateDbContextAsync();

            // 检查是否已存在
            var existingContact = await dbContext.WxContactEntities
                .FirstOrDefaultAsync(c => c.WxManagerId == wxManagerId && c.WcId == contactData.UserName, cancellationToken);

            if (existingContact != null)
            {
                // 更新现有联系人
                existingContact.NickName = contactEntity.NickName;
                existingContact.Remark = contactEntity.Remark;
                existingContact.Signature = contactEntity.Signature;
                existingContact.Sex = contactEntity.Sex;
                existingContact.AliasName = contactEntity.AliasName;
                existingContact.Country = contactEntity.Country;
                existingContact.Province = contactEntity.Province;
                existingContact.City = contactEntity.City;
                existingContact.BigHead = contactEntity.BigHead;
                existingContact.SmallHead = contactEntity.SmallHead;
                existingContact.LabelList = contactEntity.LabelList;
                existingContact.V1 = contactEntity.V1;
                existingContact.V1 = contactEntity.V1;
                existingContact.UpdatedAt = DateTime.UtcNow;

                dbContext.WxContactEntities.Update(existingContact);
                _logger.LogDebug("更新现有个人联系人 - UserName: {UserName}", contactData.UserName);
            }
            else
            {
                // 添加新联系人
                await dbContext.WxContactEntities.AddAsync(contactEntity, cancellationToken);
                _logger.LogDebug("添加新个人联系人 - UserName: {UserName}", contactData.UserName);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 保存个人联系人详情失败 - UserName: {UserName}", contactData.UserName);
            return false;
        }
    }

    /// <summary>
    /// 保存企业联系人详情到数据库
    /// </summary>
    private async Task<bool> SaveEnterpriseContactDetailAsync(Guid wxManagerId, EYunGetOpenImContactData contactData, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var contactMappingService = serviceProvider.GetRequiredService<IContactMappingService>();
            var dbContextFactory = serviceProvider.GetRequiredService<Microsoft.EntityFrameworkCore.IDbContextFactory<HappyWechat.Infrastructure.Identity.Repositories.ApplicationDbContext>>();

            // 映射企业联系人数据
            var contactEntity = await contactMappingService.MapEnterpriseContactAsync(contactData, wxManagerId, WxContactType.Enterprise);

            // 保存到数据库
            using var dbContext = await dbContextFactory.CreateDbContextAsync();

            // 检查是否已存在
            var existingContact = await dbContext.WxContactEntities
                .FirstOrDefaultAsync(c => c.WxManagerId == wxManagerId && c.WcId == contactData.UserName, cancellationToken);

            if (existingContact != null)
            {
                // 更新现有企业联系人
                existingContact.NickName = contactEntity.NickName;
                existingContact.Remark = contactEntity.Remark;
                existingContact.Signature = contactEntity.Signature;
                existingContact.Sex = contactEntity.Sex;
                existingContact.BigHead = contactEntity.BigHead;
                existingContact.SmallHead = contactEntity.SmallHead;
                existingContact.V1 = contactEntity.V1;
                existingContact.UpdatedAt = DateTime.UtcNow;

                // 企业联系人特有字段设置为null（按需求规范）
                existingContact.AliasName = null;
                existingContact.Country = null;
                existingContact.Province = null;
                existingContact.City = null;
                existingContact.LabelList = null;

                dbContext.WxContactEntities.Update(existingContact);
                _logger.LogDebug("更新现有企业联系人 - UserName: {UserName}", contactData.UserName);
            }
            else
            {
                // 添加新企业联系人
                await dbContext.WxContactEntities.AddAsync(contactEntity, cancellationToken);
                _logger.LogDebug("添加新企业联系人 - UserName: {UserName}", contactData.UserName);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 保存企业联系人详情失败 - UserName: {UserName}", contactData.UserName);
            return false;
        }
    }
}