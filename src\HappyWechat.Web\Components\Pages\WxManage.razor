@page "/wxManage"
@using HappyWechat.Application.Commons
@using HappyWechat.Application.DTOs.Requests.Commands
@using HappyWechat.Application.DTOs.Requests.Queries
@using HappyWechat.Domain.ValueObjects.Enums
@using HappyWechat.Web.Components.Wx
@using HappyWechat.Web.FrontApis
@using HappyWechat.Web.VOs
@using Microsoft.JSInterop
@inject IDialogService DialogService;
@inject WxApi WxApi;
@inject ISnackbar Snackbar;
@inject IJSRuntime JSRuntime;
@implements IDisposable

<MudStack Row="true" Class="ma-2">
    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="HandleFirstTimeLogin">
        添加微信
    </MudButton>
    <MudButton Variant="Variant.Outlined" Color="Color.Info" OnClick="RefreshList">
        刷新列表
    </MudButton>
</MudStack>

@if (!_wxUsers.Any())
{
    <MudAlert Severity="Severity.Info" Class="ma-4">
        <MudText Typo="Typo.h6">欢迎使用微信管理系统</MudText>
        <MudText Class="mt-2">您还没有配置微信账户。点击"添加微信"按钮开始设置您的第一个微信账户。</MudText>
        <MudList T="string" Class="mt-2">
            <MudListItem T="string" Icon="@Icons.Material.Filled.QrCode" Text="选择地区和设备获取二维码" />
            <MudListItem T="string" Icon="@Icons.Material.Filled.Phone" Text="使用微信APP扫描二维码" />
            <MudListItem T="string" Icon="@Icons.Material.Filled.Sync" Text="登录成功后同步通讯录" />
        </MudList>
    </MudAlert>
}
else
{
    <MudPaper Class="ma-2">
        <MudTable Items="@_wxUsers" Hover="true" Loading="@_isLoading">
            <HeaderContent>
                <MudTh>头像</MudTh>
                <MudTh>昵称</MudTh>
                <MudTh>微信号</MudTh>
                <MudTh>手机号</MudTh>
                <MudTh>性别</MudTh>
                <MudTh>设备类型</MudTh>
                <MudTh>状态</MudTh>
                <MudTh>启用状态</MudTh>
                <MudTh>离线通知邮箱</MudTh>
                <MudTh>操作</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="头像">
                    @if (!string.IsNullOrEmpty(context.HeadUrl) && !_imageErrorAccounts.Contains(context.Id))
                    {
                        <MudAvatar Size="Size.Medium">
                            <MudImage Src="@context.HeadUrl"
                                      Alt="@context.NickName"
                                      Width="32"
                                      Height="32"
                                      ObjectFit="ObjectFit.Cover"
                                      Style="border-radius: 50%;"
                                      Loading="Loading.Lazy"
                                      @onerror="@(() => HandleImageError(context))" />
                        </MudAvatar>
                    }
                    else
                    {
                        <MudAvatar Color="Color.Secondary" Size="Size.Small">
                            @(string.IsNullOrEmpty(context.NickName) ? "?" : context.NickName.Substring(0, 1))
                        </MudAvatar>
                    }
                </MudTd>
                <MudTd DataLabel="昵称">@context.NickName</MudTd>
                <MudTd DataLabel="微信号">@context.WAccount</MudTd>
                <MudTd DataLabel="手机号">@context.MobilePhone</MudTd>
                <MudTd DataLabel="性别">@(context.Sex == 1 ? "男" : context.Sex == 2 ? "女" : "未知")</MudTd>
                <MudTd DataLabel="设备类型">
                    <MudChip T="string" Color="Color.Info" Size="Size.Small">
                        @(context.DeviceType ?? "ipad")
                    </MudChip>
                </MudTd>
                <MudTd DataLabel="状态">
                    @if (context.WxStatus == WxStatus.AlreadyLogIn)
                    {
                        <MudChip T="string" Color="Color.Success">已登录</MudChip>
                    }
                    else
                    {
                        <MudChip T="string" Color="Color.Default">未登录</MudChip>
                    }
                </MudTd>
                <MudTd DataLabel="启用状态">
                    <MudSwitch T="bool"
                               @bind-Value="@context.IsEnabled"
                               @bind-Value:after="@(() => HandleAccountEnabledChangedAsync(context))"
                               Color="Color.Success"
                               ThumbIcon="@(context.IsEnabled ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)"
                               ThumbIconColor="@(context.IsEnabled ? Color.Success : Color.Error)"
                               Label="@(context.IsEnabled ? "启用" : "停用")" />
                </MudTd>
                <MudTd DataLabel="离线通知邮箱">
                    <MudTextField T="string"
                                  @bind-Value="@context.OfflineNotificationEmail"
                                  @bind-Value:after="@(() => HandleOfflineEmailChangedAsync(context))"
                                  Placeholder="输入邮箱地址"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  Immediate="false"
                                  DebounceInterval="1000"
                                  Style="min-width: 200px;" />
                </MudTd>
                <MudTd DataLabel="操作">
                    <MudButtonGroup Size="Size.Small" Variant="Variant.Outlined">
                        @if (context.WxStatus != WxStatus.AlreadyLogIn)
                        {
                            <MudLoadingButton Loading="@(_accountOperationStates.GetValueOrDefault(context.Id, false))" 
                                            Color="Color.Primary" 
                                            OnClick="() => HandleAccountLogin(context)">
                                登录
                            </MudLoadingButton>
                        }
                        else
                        {
                            <MudLoadingButton Loading="@(_accountOperationStates.GetValueOrDefault(context.Id, false))"
                                            Color="Color.Secondary" 
                                            OnClick="() => SyncContactGroup(context)">
                                同步通讯录
                            </MudLoadingButton>
                            <MudLoadingButton Loading="@(_accountOperationStates.GetValueOrDefault(context.Id, false))"
                                            Color="Color.Warning" 
                                            OnClick="() => HandleAccountLogout(context)">
                                登出
                            </MudLoadingButton>
                        }
                        <MudLoadingButton Loading="@(_accountOperationStates.GetValueOrDefault(context.Id, false))"
                                        Color="Color.Error" 
                                        OnClick="() => HandleAccountDelete(context)">
                            删除
                        </MudLoadingButton>
                    </MudButtonGroup>
                </MudTd>
            </RowTemplate>
        </MudTable>
    </MudPaper>
}

@code {
    private List<WxManagerVo> _wxUsers = [];
    private bool _isLoading = false;
    private Dictionary<Guid, bool> _accountOperationStates = new(); // 跟踪每个账户的操作状态
    private HashSet<Guid> _imageErrorAccounts = new(); // 跟踪头像加载失败的账户
    private DotNetObjectReference<WxManage>? _objRef;

    protected override async Task OnInitializedAsync()
    {
        _objRef = DotNetObjectReference.Create(this);
        await RefreshData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                // 注册SignalR页面刷新回调
                await JSRuntime.InvokeVoidAsync("registerPageRefresh", "wxaccounts", _objRef);
                // 🔧 注释冗余的SignalR注册日志 - 减少日志噪音
                // Console.WriteLine("✅ WxManage页面已注册SignalR刷新回调");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 注册SignalR刷新回调失败: {ex.Message}");
            }
        }
    }

    [JSInvokable]
    public async Task OnSimplifiedRefresh(object refreshData)
    {
        try
        {
            Console.WriteLine($"🔄 收到SignalR刷新通知，正在刷新微信账户列表...");
            await RefreshData();
            Snackbar.Add("账户列表已自动刷新", Severity.Success);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ SignalR刷新失败: {ex.Message}");
            Snackbar.Add("自动刷新失败，请手动刷新", Severity.Warning);
        }
    }

    private async Task RefreshData()
    {
        try
        {
            _isLoading = true;
            PageResponse<WxManagerVo> mangedWxList = await WxApi.getMangedWxList(new GetManagedWxListQuery()
            {
                PageQuery = new PageQuery()
                {
                    Page = 1,
                    PageSize = 1000
                }
            });
            _wxUsers = mangedWxList.Items;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取微信账户列表失败: {ex.Message}");
            Snackbar.Add("获取微信账户列表失败", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }
    
    private async Task RefreshList()
    {
        await RefreshData();
        Snackbar.Add("列表已刷新", Severity.Success);
    }

    private async Task HandleFirstTimeLogin()
    {
        try
        {
            var dialogOptions = new DialogOptions()
            {
                MaxWidth = MaxWidth.Small,
                FullWidth = true,
                Position = DialogPosition.Center,
                NoHeader = true
            };
            
            var parameters = new DialogParameters()
            {
                { "AccountId", (Guid?)null } // 首次登录，不传AccountId
            };
            
            var result = await DialogService.ShowAsync<WxDeviceLoginDialog>("首次登录", parameters, dialogOptions);
            
            if (result.Result is { IsCanceled: false })
            {
                // SignalR会自动触发页面刷新，无需手动延迟和重试
                Snackbar.Add("登录请求已提交，正在处理...", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"首次登录过程失败: {ex.Message}");
            var errorMessage = ex.Message.Contains("登录微信号数量已达上限") ? 
                "登录数量已达上限，请先登出其他账户" : 
                "登录过程失败，请稍后重试";
            Snackbar.Add(errorMessage, Severity.Error);
        }
    }

    private async Task HandleAccountLogin(WxManagerVo account)
    {
        try
        {
            _accountOperationStates[account.Id] = true;
            StateHasChanged();
            
            var dialogOptions = new DialogOptions()
            {
                MaxWidth = MaxWidth.Small,
                FullWidth = true,
                Position = DialogPosition.Center,
                NoHeader = true
            };
            
            var parameters = new DialogParameters()
            {
                { "AccountId", (Guid?)account.Id } // 传递AccountId进行重登录
            };
            
            var result = await DialogService.ShowAsync<WxDeviceLoginDialog>("账户重登录", parameters, dialogOptions);
            
            if (result.Result is { IsCanceled: false })
            {
                // SignalR会自动触发页面刷新，无需手动延迟和重试
                Snackbar.Add($"账户 {account.NickName} 登录请求已提交，正在处理...", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"账户登录失败: {ex.Message}");
            var errorMessage = ex.Message.Contains("登录微信号数量已达上限") ? 
                "登录数量已达上限，请先登出其他账户" : 
                "账户登录失败，请稍后重试";
            Snackbar.Add(errorMessage, Severity.Error);
        }
        finally
        {
            _accountOperationStates[account.Id] = false;
            StateHasChanged();
        }
    }

    private async Task HandleAccountLogout(WxManagerVo account)
    {
        bool? result = await DialogService.ShowMessageBox(
            "确认登出",
            $"确定要登出账户 {account.NickName} 吗？",
            yesText: "确定", 
            cancelText: "取消");
            
        if (result == true)
        {
            try
            {
                _accountOperationStates[account.Id] = true;
                StateHasChanged();
                
                await WxApi.LogoutAccount(account.Id);
                await RefreshData();
                Snackbar.Add($"账户 {account.NickName} 已登出", Severity.Success);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"账户登出失败: {ex.Message}");
                Snackbar.Add("账户登出失败", Severity.Error);
            }
            finally
            {
                _accountOperationStates[account.Id] = false;
                StateHasChanged();
            }
        }
    }

    private async Task HandleAccountDelete(WxManagerVo account)
    {
        bool? result = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除账户 {account.NickName} 吗？这将删除所有相关数据且无法恢复。",
            yesText: "确定", 
            cancelText: "取消");
            
        if (result == true)
        {
            try
            {
                _accountOperationStates[account.Id] = true;
                StateHasChanged();
                
                await WxApi.DeleteAccount(account.Id);
                await RefreshData();
                Snackbar.Add($"账户 {account.NickName} 已删除", Severity.Success);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"账户删除失败: {ex.Message}");
                Snackbar.Add("账户删除失败", Severity.Error);
            }
            finally
            {
                _accountOperationStates[account.Id] = false;
                StateHasChanged();
            }
        }
    }

    public async Task SyncContactGroup(WxManagerVo account)
    {
        try
        {
            _accountOperationStates[account.Id] = true;
            StateHasChanged();
            
            var result = await WxApi.InitAndSyncAddressList(new WxInitAndSyncAddressListCommand());
            
            if (result == null || !result.IsSuccess)
            {
                var errorMsg = !string.IsNullOrEmpty(result?.ErrorMessage) ? result.ErrorMessage : "微信账号可能已注销或二维码失效，请重新登录";
                await DialogService.ShowMessageBox("同步失败", errorMsg);
                // 如果微信账号状态发生变化，SignalR会自动通知刷新
            }
            else
            {
                // 显示统计信息弹窗
                var statsMessage = $"通讯录同步完成！\n\n" +
                                 $"📊 统计信息：\n" +
                                 $"• 好友：{result.TotalFriends} 个（新增：{result.NewFriends}，删除：{result.RemovedFriends}）\n" +
                                 $"• 群聊：{result.TotalChatrooms} 个（新增：{result.NewChatrooms}，删除：{result.RemovedChatrooms}）\n" +
                                 $"• 企业微信：{result.TotalEnterprise} 个（新增：{result.NewEnterprise}，删除：{result.RemovedEnterprise}）\n\n" +
                                 $"🔄 总计：{result.TotalContactCount} 个联系人\n" +
                                 $"✅ 新增：{result.TotalNewCount} 个\n" +
                                 $"❌ 删除：{result.TotalRemovedCount} 个\n\n" +
                                 $"⏰ 同步时间：{result.SyncTime:yyyy-MM-dd HH:mm:ss}";
                
                await DialogService.ShowMessageBox("同步完成", statsMessage);
                
                // 显示 Snackbar 通知
                if (result.TotalNewCount > 0 || result.TotalRemovedCount > 0)
                {
                    Snackbar.Add($"通讯录已更新：新增 {result.TotalNewCount} 个，删除 {result.TotalRemovedCount} 个", Severity.Info);
                }
                else
                {
                    Snackbar.Add("通讯录同步完成，无变化", Severity.Success);
                }
            }
        }
        catch (Exception ex)
        {
            string errorMsg = ex.Message.Contains("微信已注销") || ex.Message.Contains("请重新登录") 
                ? "微信账号已注销或二维码失效，请重新登录" 
                : $"同步过程发生错误: {ex.Message}";
                
            await DialogService.ShowMessageBox("同步失败", errorMsg);
            // 如果微信账号状态发生变化，SignalR会自动通知刷新
        }
        finally
        {
            _accountOperationStates[account.Id] = false;
            StateHasChanged();
        }
    }

    private void HandleImageError(WxManagerVo account)
    {
        _imageErrorAccounts.Add(account.Id);
        StateHasChanged();
    }

    /// <summary>
    /// 处理账号启用状态变化（统一绑定模式）
    /// </summary>
    private async Task HandleAccountEnabledChangedAsync(WxManagerVo account)
    {
        try
        {
            _accountOperationStates[account.Id] = true;
            StateHasChanged();

            // 调用API更新账号启用状态
            await WxApi.UpdateAccountEnabled(account.Id, account.IsEnabled);

            var statusText = account.IsEnabled ? "启用" : "停用";
            Snackbar.Add($"账户 {account.NickName} 已{statusText}", Severity.Success);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新账号启用状态失败: {ex.Message}");
            Snackbar.Add("更新账号状态失败", Severity.Error);

            // 恢复原状态
            account.IsEnabled = !account.IsEnabled;
            StateHasChanged();
        }
        finally
        {
            _accountOperationStates[account.Id] = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 处理离线通知邮箱变化（统一绑定模式）
    /// </summary>
    private async Task HandleOfflineEmailChangedAsync(WxManagerVo account)
    {
        try
        {
            // 简单的邮箱格式验证
            if (!string.IsNullOrEmpty(account.OfflineNotificationEmail) && !IsValidEmail(account.OfflineNotificationEmail))
            {
                Snackbar.Add("请输入有效的邮箱地址", Severity.Warning);
                // 清空无效邮箱
                account.OfflineNotificationEmail = "";
                StateHasChanged();
                return;
            }

            _accountOperationStates[account.Id] = true;
            StateHasChanged();

            // 调用API更新离线通知邮箱
            await WxApi.UpdateOfflineNotificationEmail(account.Id, account.OfflineNotificationEmail);

            if (string.IsNullOrEmpty(account.OfflineNotificationEmail))
            {
                Snackbar.Add($"账户 {account.NickName} 的离线通知邮箱已清空", Severity.Info);
            }
            else
            {
                Snackbar.Add($"账户 {account.NickName} 的离线通知邮箱已更新", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新离线通知邮箱失败: {ex.Message}");
            Snackbar.Add("更新离线通知邮箱失败", Severity.Error);

            // 从服务器重新获取最新状态
            await RefreshData();
        }
        finally
        {
            _accountOperationStates[account.Id] = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// 简单的邮箱格式验证
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public void Dispose()
    {
        try
        {
            // 取消注册SignalR页面刷新回调
            JSRuntime?.InvokeVoidAsync("unregisterPageRefresh", "wxaccounts");
            _objRef?.Dispose();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ 清理SignalR注册失败: {ex.Message}");
        }
    }
}