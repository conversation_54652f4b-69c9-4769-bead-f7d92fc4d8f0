using Microsoft.Extensions.Logging;
using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// 联系人批量处理服务
/// </summary>
public class ContactBatchProcessor
{
    private readonly ILogger<ContactBatchProcessor> _logger;
    private const int MAX_BATCH_SIZE = 20; // /getContact接口最多支持20个ID

    public ContactBatchProcessor(ILogger<ContactBatchProcessor> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 将联系人ID列表分批处理
    /// </summary>
    /// <param name="contactIds">联系人ID列表</param>
    /// <param name="listType">联系人类型</param>
    /// <returns>分批后的联系人ID列表</returns>
    public List<List<string>> CreateBatches(List<string> contactIds, WxContactListType listType)
    {
        if (contactIds == null || !contactIds.Any())
        {
            _logger.LogWarning("联系人ID列表为空，无法创建批次");
            return new List<List<string>>();
        }

        var batches = new List<List<string>>();

        if (listType == WxContactListType.Friends)
        {
            // 个人微信好友：批量处理，每批最多20个
            for (int i = 0; i < contactIds.Count; i += MAX_BATCH_SIZE)
            {
                var batch = contactIds.Skip(i).Take(MAX_BATCH_SIZE).ToList();
                batches.Add(batch);
            }
            
            _logger.LogInformation("个人联系人分批完成 - 总数: {Total}, 批次数: {BatchCount}, 每批大小: 最多{BatchSize}", 
                contactIds.Count, batches.Count, MAX_BATCH_SIZE);
        }
        else if (listType == WxContactListType.Enterprise)
        {
            // 企业微信联系人：单个处理
            batches.AddRange(contactIds.Select(id => new List<string> { id }));
            
            _logger.LogInformation("企业联系人分批完成 - 总数: {Total}, 批次数: {BatchCount} (单个处理)", 
                contactIds.Count, batches.Count);
        }
        else
        {
            _logger.LogWarning("不支持的联系人类型: {ListType}", listType);
            return new List<List<string>>();
        }

        return batches;
    }

    /// <summary>
    /// 将批次中的联系人ID用逗号连接
    /// </summary>
    /// <param name="batch">批次联系人ID列表</param>
    /// <returns>逗号分隔的联系人ID字符串</returns>
    public string JoinBatchIds(List<string> batch)
    {
        if (batch == null || !batch.Any())
        {
            _logger.LogWarning("批次为空，无法连接ID");
            return string.Empty;
        }

        var joinedIds = string.Join(",", batch);
        _logger.LogDebug("批次ID连接完成 - 数量: {Count}, 结果: {JoinedIds}", batch.Count, joinedIds);
        return joinedIds;
    }

    /// <summary>
    /// 验证批次大小是否合法
    /// </summary>
    /// <param name="batch">批次</param>
    /// <param name="listType">联系人类型</param>
    /// <returns>是否合法</returns>
    public bool ValidateBatchSize(List<string> batch, WxContactListType listType)
    {
        if (batch == null || !batch.Any())
        {
            _logger.LogWarning("批次为空");
            return false;
        }

        if (listType == WxContactListType.Friends && batch.Count > MAX_BATCH_SIZE)
        {
            _logger.LogError("个人联系人批次大小超限 - 当前: {Current}, 最大: {Max}", batch.Count, MAX_BATCH_SIZE);
            return false;
        }

        if (listType == WxContactListType.Enterprise && batch.Count > 1)
        {
            _logger.LogError("企业联系人批次大小错误 - 当前: {Current}, 应为: 1", batch.Count);
            return false;
        }

        return true;
    }

    /// <summary>
    /// 计算总批次数
    /// </summary>
    /// <param name="totalCount">总联系人数</param>
    /// <param name="listType">联系人类型</param>
    /// <returns>批次数</returns>
    public int CalculateTotalBatches(int totalCount, WxContactListType listType)
    {
        if (totalCount <= 0) return 0;

        return listType switch
        {
            WxContactListType.Friends => (int)Math.Ceiling((double)totalCount / MAX_BATCH_SIZE),
            WxContactListType.Enterprise => totalCount, // 企业联系人一个一个处理
            _ => 0
        };
    }

    /// <summary>
    /// 获取批次处理描述
    /// </summary>
    /// <param name="batchIndex">当前批次索引（从0开始）</param>
    /// <param name="totalBatches">总批次数</param>
    /// <param name="batchSize">当前批次大小</param>
    /// <param name="listType">联系人类型</param>
    /// <returns>描述字符串</returns>
    public string GetBatchDescription(int batchIndex, int totalBatches, int batchSize, WxContactListType listType)
    {
        var contactTypeName = listType == WxContactListType.Friends ? "个人" : "企业";
        return $"正在处理{contactTypeName}联系人 - 批次 {batchIndex + 1}/{totalBatches} (本批 {batchSize} 个)";
    }
}
