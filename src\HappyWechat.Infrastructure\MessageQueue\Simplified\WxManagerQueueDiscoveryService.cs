using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.Options;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified;

/// <summary>
/// 微信管理器队列发现服务
/// 动态发现系统中活跃的微信管理器，为每个管理器管理独立的队列
/// </summary>
public class WxManagerQueueDiscoveryService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ISimplifiedQueueService _queueService;
    private readonly ILogger<WxManagerQueueDiscoveryService> _logger;
    private readonly RedisQueueOptions _options;
    
    // 缓存活跃的微信管理器信息
    private readonly Dictionary<Guid, WxManagerInfo> _activeWxManagers = new();
    private DateTime _lastDiscoveryTime = DateTime.MinValue;
    private readonly object _discoveryLock = new();

    public WxManagerQueueDiscoveryService(
        IServiceProvider serviceProvider,
        ISimplifiedQueueService queueService,
        ILogger<WxManagerQueueDiscoveryService> logger,
        IOptions<RedisQueueOptions> options)
    {
        _serviceProvider = serviceProvider;
        _queueService = queueService;
        _logger = logger;
        _options = options.Value;
    }

    /// <summary>
    /// 发现当前活跃的微信管理器
    /// </summary>
    public async Task<List<WxManagerInfo>> DiscoverActiveWxManagersAsync(CancellationToken cancellationToken = default)
    {
        lock (_discoveryLock)
        {
            // 如果距离上次发现时间不到30秒，直接返回缓存
            if (DateTime.UtcNow - _lastDiscoveryTime < TimeSpan.FromSeconds(30))
            {
                return _activeWxManagers.Values.ToList();
            }
        }

        try
        {
            var discoveredManagers = new Dictionary<Guid, WxManagerInfo>();
            
            // 方法1: 从Redis队列中发现活跃的微信管理器
            await DiscoverFromRedisQueuesAsync(discoveredManagers, cancellationToken);
            
            // 方法2: 从数据库中获取已登录的微信管理器
            await DiscoverFromDatabaseAsync(discoveredManagers, cancellationToken);
            
            lock (_discoveryLock)
            {
                // 更新缓存
                _activeWxManagers.Clear();
                foreach (var kvp in discoveredManagers)
                {
                    _activeWxManagers[kvp.Key] = kvp.Value;
                }
                _lastDiscoveryTime = DateTime.UtcNow;
                
                // 🔧 注释冗余的发现完成日志 - 减少日志噪音
                // _logger.LogInformation("🔍 微信管理器发现完成 - 活跃数量: {Count}", _activeWxManagers.Count);
                
                return _activeWxManagers.Values.ToList();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发现微信管理器失败");
            return new List<WxManagerInfo>();
        }
    }

    /// <summary>
    /// 从Redis队列中发现活跃的微信管理器
    /// </summary>
    private async Task DiscoverFromRedisQueuesAsync(Dictionary<Guid, WxManagerInfo> discoveredManagers, CancellationToken cancellationToken)
    {
        try
        {
            var queueStatus = await _queueService.GetAllQueueStatusAsync(cancellationToken);
            var queuePattern = $"{_options.KeyPrefix}:queue:";
            
            foreach (var kvp in queueStatus)
            {
                var queueName = kvp.Key;
                var messageCount = kvp.Value;
                
                if (queueName.StartsWith(queuePattern) && messageCount > 0)
                {
                    // 解析队列名称: hw:queue:{wxManagerId}:{queueType}
                    var parts = queueName.Split(':');
                    if (parts.Length >= 3 && Guid.TryParse(parts[2], out var wxManagerId))
                    {
                        if (!discoveredManagers.ContainsKey(wxManagerId))
                        {
                            discoveredManagers[wxManagerId] = new WxManagerInfo
                            {
                                WxManagerId = wxManagerId,
                                DiscoverySource = "Redis队列",
                                HasPendingMessages = true,
                                LastActivityTime = DateTime.UtcNow
                            };
                        }
                        else
                        {
                            discoveredManagers[wxManagerId].HasPendingMessages = true;
                        }
                    }
                }
            }
            
            // 🔧 注释冗余的Debug日志 - 减少日志噪音
            // _logger.LogDebug("🔍 从Redis队列发现微信管理器 - Count: {Count}", discoveredManagers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 从Redis队列发现微信管理器失败");
        }
    }

    /// <summary>
    /// 从数据库中获取已登录的微信管理器
    /// </summary>
    private async Task DiscoverFromDatabaseAsync(Dictionary<Guid, WxManagerInfo> discoveredManagers, CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var wxManagerService = scope.ServiceProvider.GetService<IWxManagerService>();
            
            if (wxManagerService != null)
            {
                var loggedInManagers = await wxManagerService.GetLoggedInManagersAsync(cancellationToken);
                
                foreach (var manager in loggedInManagers)
                {
                    if (!discoveredManagers.ContainsKey(manager.Id))
                    {
                        discoveredManagers[manager.Id] = new WxManagerInfo
                        {
                            WxManagerId = manager.Id,
                            WxId = manager.WxId,
                            NickName = manager.NickName,
                            DiscoverySource = "数据库",
                            HasPendingMessages = false,
                            LastActivityTime = manager.LastActiveTime ?? DateTime.UtcNow
                        };
                    }
                    else
                    {
                        // 更新信息
                        var info = discoveredManagers[manager.Id];
                        info.WxId = manager.WxId;
                        info.NickName = manager.NickName;
                        info.LastActivityTime = manager.LastActiveTime ?? info.LastActivityTime;
                    }
                }
                
                // 🔧 注释冗余的Debug日志 - 减少日志噪音
                // _logger.LogDebug("🔍 从数据库发现微信管理器 - Count: {Count}", loggedInManagers.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 从数据库发现微信管理器失败");
        }
    }

    /// <summary>
    /// 获取特定微信管理器的队列统计信息
    /// </summary>
    public async Task<WxManagerQueueStats> GetWxManagerQueueStatsAsync(Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new WxManagerQueueStats { WxManagerId = wxManagerId };
            var queueTypes = new[] { "contact:detail", "contact:friends", "contact:groups", "contact:enterprise", "group:sync", "ai:processing", "wx:callback", "file:text", "file:image", "file:voice", "file:video", "file:general" };
            
            foreach (var queueType in queueTypes)
            {
                var length = await _queueService.GetQueueLengthAsync(wxManagerId, queueType, cancellationToken);
                if (length > 0)
                {
                    stats.QueueLengths[queueType] = length;
                    stats.TotalMessages += length;
                }
            }
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取微信管理器队列统计失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new WxManagerQueueStats { WxManagerId = wxManagerId };
        }
    }
}

/// <summary>
/// 微信管理器信息
/// </summary>
public class WxManagerInfo
{
    public Guid WxManagerId { get; set; }
    public string? WxId { get; set; }
    public string? NickName { get; set; }
    public string DiscoverySource { get; set; } = string.Empty;
    public bool HasPendingMessages { get; set; }
    public DateTime LastActivityTime { get; set; }
}

/// <summary>
/// 微信管理器队列统计信息
/// </summary>
public class WxManagerQueueStats
{
    public Guid WxManagerId { get; set; }
    public Dictionary<string, long> QueueLengths { get; set; } = new();
    public long TotalMessages { get; set; }
    public DateTime StatTime { get; set; } = DateTime.UtcNow;
}