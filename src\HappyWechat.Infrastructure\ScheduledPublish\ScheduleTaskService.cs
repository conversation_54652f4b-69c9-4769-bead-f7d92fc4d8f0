using AutoMapper;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.Entities;
using HappyWechat.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.ScheduledPublish;

/// <summary>
/// 定时任务服务实现
/// </summary>
public class ScheduleTaskService : IScheduleTaskService
{
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    private readonly IMapper _mapper;
    private readonly ILogger<ScheduleTaskService> _logger;

    public ScheduleTaskService(
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        IMapper mapper,
        ILogger<ScheduleTaskService> logger)
    {
        _dbContextFactory = dbContextFactory;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task CreateTask(ScheduleTaskDto scheduleTaskDto, Guid userId)
    {
        try
        {
            _logger.LogInformation("创建定时任务 - 用户ID: {UserId}, 任务名称: {TaskName}", userId, scheduleTaskDto.DisplayName);

            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var entity = new ScheduleTaskEntity
            {
                Id = Guid.NewGuid(),
                DisplayName = scheduleTaskDto.DisplayName,
                CronExpression = scheduleTaskDto.CronExpression,
                DurationType = scheduleTaskDto.DurationType,
                Time = scheduleTaskDto.Time,
                Enabled = scheduleTaskDto.Enabled,
                WxManagerId = scheduleTaskDto.WxManagerId,
                TaskType = scheduleTaskDto.TaskType,
                MaterialSelectionMode = scheduleTaskDto.MaterialSelectionMode,
                CurrentMaterialIndex = 0,
                UserId = userId,
                
                // 序列化JSON字段
                WeeklyDaysJson = JsonSerializer.Serialize(scheduleTaskDto.WeeklyDays ?? []),
                MonthlyDaysJson = JsonSerializer.Serialize(scheduleTaskDto.MonthlyDays ?? []),
                TargetGroupsJson = JsonSerializer.Serialize(scheduleTaskDto.TargetGroups ?? []),
                TargetContactsJson = JsonSerializer.Serialize(scheduleTaskDto.TargetContacts ?? []),
                MaterialIdsJson = JsonSerializer.Serialize(scheduleTaskDto.MaterialIds ?? [])
            };

            context.ScheduleTaskEntities.Add(entity);
            await context.SaveChangesAsync();

            _logger.LogInformation("定时任务创建成功 - 任务ID: {TaskId}", entity.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建定时任务失败 - 用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<PageResponse<ScheduleTaskDto>> ListTasks(Guid userId, ScheduleTaskQuery query)
    {
        try
        {
            _logger.LogInformation("获取定时任务列表 - 用户ID: {UserId}", userId);

            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var queryable = context.ScheduleTaskEntities
                .Where(t => t.UserId == userId)
                .AsQueryable();

            // 应用过滤条件
            if (!string.IsNullOrWhiteSpace(query.DisplayName))
            {
                queryable = queryable.Where(t => t.DisplayName.Contains(query.DisplayName));
            }

            if (query.Enabled.HasValue)
            {
                queryable = queryable.Where(t => t.Enabled == query.Enabled.Value);
            }

            if (query.TaskType.HasValue)
            {
                queryable = queryable.Where(t => t.TaskType == query.TaskType.Value);
            }

            // 计算总数
            var totalCount = await queryable.CountAsync();

            // 应用分页
            var pageQuery = query.PageQuery ?? new PageQuery();
            var entities = await queryable
                .OrderByDescending(t => t.Id)
                .Skip((pageQuery.Page - 1) * pageQuery.PageSize)
                .Take(pageQuery.PageSize)
                .ToListAsync();

            // 转换为DTO
            var dtos = entities.Select(entity => new ScheduleTaskDto
            {
                Id = entity.Id,
                DisplayName = entity.DisplayName,
                CronExpression = entity.CronExpression,
                DurationType = entity.DurationType,
                Time = entity.Time,
                Enabled = entity.Enabled,
                WxManagerId = entity.WxManagerId,
                TaskType = entity.TaskType,
                MaterialSelectionMode = entity.MaterialSelectionMode,
                
                // 反序列化JSON字段
                WeeklyDays = JsonSerializer.Deserialize<IEnumerable<DayOfWeek>>(entity.WeeklyDaysJson ?? "[]") ?? [],
                MonthlyDays = JsonSerializer.Deserialize<IEnumerable<int>>(entity.MonthlyDaysJson ?? "[]") ?? [],
                TargetGroups = JsonSerializer.Deserialize<IEnumerable<string>>(entity.TargetGroupsJson ?? "[]") ?? [],
                TargetContacts = JsonSerializer.Deserialize<IEnumerable<string>>(entity.TargetContactsJson ?? "[]") ?? [],
                MaterialIds = (JsonSerializer.Deserialize<IEnumerable<Guid>>(entity.MaterialIdsJson ?? "[]") ?? []).ToList()
            }).ToList();

            var result = new PageResponse<ScheduleTaskDto>
            {
                Items = dtos,
                TotalCount = totalCount,
                Page = pageQuery.Page,
                PageSize = pageQuery.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageQuery.PageSize),
                HasNextPage = pageQuery.Page < (int)Math.Ceiling((double)totalCount / pageQuery.PageSize),
                HasPreviousPage = pageQuery.Page > 1
            };

            _logger.LogInformation("获取定时任务列表成功 - 用户ID: {UserId}, 总数: {TotalCount}", userId, totalCount);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取定时任务列表失败 - 用户ID: {UserId}", userId);
            throw;
        }
    }

    public async Task Enable(EnabledScheduleTaskCommand command)
    {
        try
        {
            _logger.LogInformation("启用/禁用定时任务 - 任务ID: {TaskId}, 启用状态: {Enabled}", command.TaskId, command.Enabled);

            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var entity = await context.ScheduleTaskEntities
                .FirstOrDefaultAsync(t => t.Id == command.TaskId);

            if (entity == null)
            {
                throw new InvalidOperationException($"定时任务不存在: {command.TaskId}");
            }

            entity.Enabled = command.Enabled;

            await context.SaveChangesAsync();

            _logger.LogInformation("定时任务状态更新成功 - 任务ID: {TaskId}", command.TaskId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启用/禁用定时任务失败 - 任务ID: {TaskId}", command.TaskId);
            throw;
        }
    }

    public async Task DeleteTask(Guid taskId, Guid userId)
    {
        try
        {
            _logger.LogInformation("删除定时任务 - 任务ID: {TaskId}, 用户ID: {UserId}", taskId, userId);

            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var entity = await context.ScheduleTaskEntities
                .FirstOrDefaultAsync(t => t.Id == taskId && t.UserId == userId);

            if (entity == null)
            {
                throw new InvalidOperationException($"定时任务不存在或无权限: {taskId}");
            }

            context.ScheduleTaskEntities.Remove(entity);
            await context.SaveChangesAsync();

            _logger.LogInformation("定时任务删除成功 - 任务ID: {TaskId}", taskId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除定时任务失败 - 任务ID: {TaskId}", taskId);
            throw;
        }
    }

    public async Task UpdateTask(ScheduleTaskDto scheduleTaskDto, Guid userId)
    {
        try
        {
            _logger.LogInformation("更新定时任务 - 任务ID: {TaskId}, 用户ID: {UserId}", scheduleTaskDto.Id, userId);

            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var entity = await context.ScheduleTaskEntities
                .FirstOrDefaultAsync(t => t.Id == scheduleTaskDto.Id && t.UserId == userId);

            if (entity == null)
            {
                throw new InvalidOperationException($"定时任务不存在或无权限: {scheduleTaskDto.Id}");
            }

            // 更新字段
            entity.DisplayName = scheduleTaskDto.DisplayName;
            entity.CronExpression = scheduleTaskDto.CronExpression;
            entity.DurationType = scheduleTaskDto.DurationType;
            entity.Time = scheduleTaskDto.Time;
            entity.Enabled = scheduleTaskDto.Enabled;
            entity.WxManagerId = scheduleTaskDto.WxManagerId;
            entity.TaskType = scheduleTaskDto.TaskType;
            entity.MaterialSelectionMode = scheduleTaskDto.MaterialSelectionMode;
            
            // 更新JSON字段
            entity.WeeklyDaysJson = JsonSerializer.Serialize(scheduleTaskDto.WeeklyDays ?? []);
            entity.MonthlyDaysJson = JsonSerializer.Serialize(scheduleTaskDto.MonthlyDays ?? []);
            entity.TargetGroupsJson = JsonSerializer.Serialize(scheduleTaskDto.TargetGroups ?? []);
            entity.TargetContactsJson = JsonSerializer.Serialize(scheduleTaskDto.TargetContacts ?? []);
            entity.MaterialIdsJson = JsonSerializer.Serialize(scheduleTaskDto.MaterialIds ?? []);

            await context.SaveChangesAsync();

            _logger.LogInformation("定时任务更新成功 - 任务ID: {TaskId}", scheduleTaskDto.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新定时任务失败 - 任务ID: {TaskId}", scheduleTaskDto.Id);
            throw;
        }
    }
}
