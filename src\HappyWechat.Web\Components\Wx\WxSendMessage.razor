﻿@using HappyWechat.Application.DTOs.Requests.Commands
@using HappyWechat.Application.DTOs.Responses
@using HappyWechat.Domain.ValueObjects.Enums
@using HappyWechat.Web.FrontApis
@using HappyWechat.Web.VOs
@using HappyWechat.Web.Services
@inject WxMessageApi WxMessageApi;
@inject ISnackbar Snackbar;
@inject MaterialSelectorService MaterialSelectorService;
<MudDialog Options="new DialogOptions(){MaxWidth=MaxWidth.Large}"
           Style="background: #f5f5f5; /* 轻微灰色，简洁干净 */
        color: #333; /* 深色字体，保证可读性 */
        border-radius: 8px; /* 轻微圆角，让 UI 柔和一些 */
        padding: 16px; /* 增加一点内边距，使内容不会贴边 */">
    <DialogContent>
        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
            <MudText Typo="Typo.body1">消息类型：</MudText>
            <MudRadioGroup T="WxMessageType" @bind-Value="_wxMessageType" Row="true">
                <MudRadio Value="WxMessageType.Text">文本</MudRadio>
                <MudRadio Value="WxMessageType.Picture">图片</MudRadio>
                <MudRadio Value="WxMessageType.File">文件</MudRadio>
                <MudRadio Value="WxMessageType.Voice">语音</MudRadio>
                <MudRadio Value="WxMessageType.Video">视频</MudRadio>
                <MudRadio Value="WxMessageType.Base64File">Base64文件</MudRadio>
            </MudRadioGroup>
        </MudStack>

        @if (_wxMessageType == WxMessageType.Text)
        {
            <MudTextField T="string" Label="文本内容" @bind-Value="_textMessage" Lines="5"
                          Variant="Variant.Text"
                          Class="mb-3">
            </MudTextField>
            
            <MudButton Variant="Variant.Outlined" 
                       Color="Color.Secondary"
                       StartIcon="Icons.Material.Filled.Collections"
                       OnClick="SelectTextMaterial"
                       FullWidth="true"
                       Class="mb-3">
                从素材库选择文本
            </MudButton>
        }
        else if (_wxMessageType == WxMessageType.Picture)
        {
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary"
                       StartIcon="Icons.Material.Filled.Collections"
                       OnClick="SelectImageMaterial"
                       FullWidth="true"
                       Class="mb-3">
                从素材库选择图片
            </MudButton>
            
            @if (_selectedMaterial != null)
            {
                <MudAlert Severity="Severity.Success" Class="mb-3">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.body2">已选择图片: @_selectedMaterial.Name</MudText>
                            @if (_selectedMaterial.FileSize.HasValue)
                            {
                                <MudText Typo="Typo.caption">文件大小: @_selectedMaterial.FileSizeDisplay</MudText>
                            }
                        </div>
                        <MudIconButton Icon="Icons.Material.Filled.Clear" 
                                       Size="Size.Small" 
                                       OnClick="ClearSelectedMaterial" />
                    </div>
                </MudAlert>
            }
        }
        else if (_wxMessageType == WxMessageType.File)
        {
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary"
                       StartIcon="Icons.Material.Filled.Collections"
                       OnClick="SelectFileMaterial"
                       FullWidth="true"
                       Class="mb-3">
                从素材库选择文件
            </MudButton>
            
            @if (_selectedMaterial != null)
            {
                <MudAlert Severity="Severity.Success" Class="mb-3">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.body2">已选择文件: @_selectedMaterial.Name</MudText>
                            @if (_selectedMaterial.FileSize.HasValue)
                            {
                                <MudText Typo="Typo.caption">文件大小: @_selectedMaterial.FileSizeDisplay</MudText>
                            }
                        </div>
                        <MudIconButton Icon="Icons.Material.Filled.Clear"
                                       Size="Size.Small"
                                       OnClick="ClearSelectedMaterial" />
                    </div>
                </MudAlert>
            }
        }
        else if (_wxMessageType == WxMessageType.Voice)
        {
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       StartIcon="Icons.Material.Filled.AudioFile"
                       OnClick="SelectVoiceMaterial"
                       FullWidth="true"
                       Class="mb-3">
                从素材库选择语音
            </MudButton>

            @if (_selectedMaterial != null)
            {
                <MudAlert Severity="Severity.Success" Class="mb-3">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.body2">已选择语音: @_selectedMaterial.Name</MudText>
                            @if (_selectedMaterial.FileSize.HasValue)
                            {
                                <MudText Typo="Typo.caption">文件大小: @_selectedMaterial.FileSizeDisplay</MudText>
                            }
                        </div>
                        <MudIconButton Icon="Icons.Material.Filled.Clear"
                                       Size="Size.Small"
                                       OnClick="ClearSelectedMaterial" />
                    </div>
                </MudAlert>
            }
        }
        else if (_wxMessageType == WxMessageType.Video)
        {
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       StartIcon="Icons.Material.Filled.VideoFile"
                       OnClick="SelectVideoMaterial"
                       FullWidth="true"
                       Class="mb-3">
                从素材库选择视频
            </MudButton>

            @if (_selectedMaterial != null)
            {
                <MudAlert Severity="Severity.Success" Class="mb-3">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.body2">已选择视频: @_selectedMaterial.Name</MudText>
                            @if (_selectedMaterial.FileSize.HasValue)
                            {
                                <MudText Typo="Typo.caption">文件大小: @_selectedMaterial.FileSizeDisplay</MudText>
                            }
                        </div>
                        <MudIconButton Icon="Icons.Material.Filled.Clear"
                                       Size="Size.Small"
                                       OnClick="ClearSelectedMaterial" />
                    </div>
                </MudAlert>
            }
        }
        else if (_wxMessageType == WxMessageType.Base64File)
        {
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       StartIcon="Icons.Material.Filled.AttachFile"
                       OnClick="SelectFileMaterial"
                       FullWidth="true"
                       Class="mb-3">
                从素材库选择Base64文件
            </MudButton>

            @if (_selectedMaterial != null)
            {
                <MudAlert Severity="Severity.Warning" Class="mb-3">
                    <div class="d-flex align-center justify-space-between">
                        <div>
                            <MudText Typo="Typo.body2">已选择文件: @_selectedMaterial.Name</MudText>
                            <MudText Typo="Typo.caption" Color="Color.Warning">注意：Base64文件发送功能需要进一步完善</MudText>
                            @if (_selectedMaterial.FileSize.HasValue)
                            {
                                <MudText Typo="Typo.caption">文件大小: @_selectedMaterial.FileSizeDisplay</MudText>
                            }
                        </div>
                        <MudIconButton Icon="Icons.Material.Filled.Clear"
                                       Size="Size.Small"
                                       OnClick="ClearSelectedMaterial" />
                    </div>
                </MudAlert>
            }
        }
    </DialogContent>
    <DialogActions>
        <MudStack Row="true" Justify="Justify.Center">
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="Send"
                       Disabled="@_isSending"
                       StartIcon="@(_isSending ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Send)">
                @if (_isSending)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                    <span>发送中...</span>
                }
                else
                {
                    <span>发送</span>
                }
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Secondary"
                       OnClick="Close"
                       Disabled="@_isSending">
                关闭
            </MudButton>
        </MudStack>
    </DialogActions>
</MudDialog>






@code {
    [CascadingParameter] public IMudDialogInstance DialogInstance { get; set; }
    [Parameter] public WxContactVo? WxContactVo { get; set; }
    [Parameter] public WxManagerVo? CurrentWxManager { get; set; }
    private WxMessageType _wxMessageType = WxMessageType.Text;
    private string _textMessage = "";
    private MaterialDto? _selectedMaterial;
    private bool _isSending = false;

    private async Task Send()
    {
        // 防止重复点击
        if (_isSending)
        {
            return;
        }

        // 🔧 验证当前微信账号
        if (CurrentWxManager == null)
        {
            ResetSendingStateWithWarning("请先选择微信账号");
            return;
        }

        if (string.IsNullOrEmpty(CurrentWxManager.WId))
        {
            ResetSendingStateWithWarning("当前微信账号未登录，请先登录");
            return;
        }

        _isSending = true;
        StateHasChanged();

        try
        {
            switch (_wxMessageType)
            {
                case WxMessageType.Text:
                    if (string.IsNullOrWhiteSpace(_textMessage))
                    {
                        ResetSendingStateWithWarning("请输入文本内容");
                        return;
                    }
                    await WxMessageApi.SendTextMessage(new WxSendTextMessageCommand()
                    {
                        WId = CurrentWxManager!.WId,
                        Content = _textMessage,
                        WcId = WxContactVo!.WcId
                    });
                    break;

                case WxMessageType.Picture:
                    if (_selectedMaterial == null)
                    {
                        ResetSendingStateWithWarning("请选择图片素材");
                        return;
                    }
                    await WxMessageApi.SendImageMessage(new WxSendImageMessageCommand()
                    {
                        WId = CurrentWxManager!.WId,
                        WcId = WxContactVo!.WcId,
                        Content = _selectedMaterial.FileUrl ?? _selectedMaterial.FilePath ?? "",
                        ImageUrl = _selectedMaterial.FileUrl,
                        ImagePath = _selectedMaterial.FilePath
                    });
                    break;

                case WxMessageType.File:
                    if (_selectedMaterial == null)
                    {
                        ResetSendingStateWithWarning("请选择文件素材");
                        return;
                    }
                    await WxMessageApi.SendFileMessage(new WxSendFileMessageCommand()
                    {
                        WId = CurrentWxManager!.WId,
                        WcId = WxContactVo!.WcId,
                        FilePath = _selectedMaterial.FileUrl ?? _selectedMaterial.FilePath ?? ""
                    });
                    break;

                case WxMessageType.Voice:
                    if (_selectedMaterial == null)
                    {
                        ResetSendingStateWithWarning("请选择语音素材");
                        return;
                    }
                    await WxMessageApi.SendVoiceMessage(new WxSendVoiceMessageCommand()
                    {
                        WId = CurrentWxManager!.WId,
                        WcId = WxContactVo!.WcId,
                        Content = _selectedMaterial.FileUrl ?? _selectedMaterial.FilePath ?? "",
                        VoiceFilePath = _selectedMaterial.FileUrl ?? _selectedMaterial.FilePath,
                        Length = 0 // 默认时长，实际应该从素材中获取
                    });
                    break;

                case WxMessageType.Video:
                    if (_selectedMaterial == null)
                    {
                        ResetSendingStateWithWarning("请选择视频素材");
                        return;
                    }
                    await WxMessageApi.SendVideoMessage(new WxSendVideoMessageCommand()
                    {
                        WId = CurrentWxManager!.WId,
                        WcId = WxContactVo!.WcId,
                        Path = _selectedMaterial.FileUrl ?? _selectedMaterial.FilePath ?? "",
                        ThumbPath = _selectedMaterial.ThumbnailUrl ?? _selectedMaterial.ThumbnailPath ?? "",
                        VideoFilePath = _selectedMaterial.FileUrl ?? _selectedMaterial.FilePath
                    });
                    break;

                case WxMessageType.Base64File:
                    if (_selectedMaterial == null)
                    {
                        ResetSendingStateWithWarning("请选择文件素材");
                        return;
                    }
                    // 对于Base64文件，需要读取文件内容并转换为Base64
                    // 这里简化处理，实际应该有专门的文件读取和转换逻辑
                    await WxMessageApi.SendBase64FileMessage(new WxSendFileBase64MessageCommand()
                    {
                        WId = CurrentWxManager!.WId,
                        WcId = WxContactVo!.WcId,
                        FileName = _selectedMaterial.Name,
                        Base64 = "" // 需要实际的Base64内容
                    });
                    break;

                default:
                    ResetSendingStateWithWarning($"暂不支持 {_wxMessageType} 类型的消息发送");
                    return;
            }

            Snackbar.Add("消息发送成功", Severity.Success);

            // 清理状态
            _textMessage = "";
            _selectedMaterial = null;

            // 关闭对话框
            DialogInstance.Close();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"发送失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            // 重置发送状态
            _isSending = false;
            StateHasChanged();
        }
    }

    private async Task Close()
    {
        DialogInstance.Close();
    }

    private async Task SelectTextMaterial()
    {
        try
        {
            var materials = await MaterialSelectorService.ShowTextSelectorAsync(false);
            if (materials?.Count > 0)
            {
                var material = materials[0];
                _textMessage = material.Content ?? "";
                Snackbar.Add("已选择文本素材", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"选择素材失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SelectImageMaterial()
    {
        try
        {
            var materials = await MaterialSelectorService.ShowImageSelectorAsync(false);
            if (materials?.Count > 0)
            {
                _selectedMaterial = materials[0];
                Snackbar.Add("已选择图片素材", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"选择素材失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SelectFileMaterial()
    {
        try
        {
            var materials = await MaterialSelectorService.ShowDocumentSelectorAsync(false);
            if (materials?.Count > 0)
            {
                _selectedMaterial = materials[0];
                Snackbar.Add("已选择文件素材", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"选择素材失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SelectVoiceMaterial()
    {
        try
        {
            var materials = await MaterialSelectorService.ShowAudioSelectorAsync(false);
            if (materials?.Count > 0)
            {
                _selectedMaterial = materials[0];
                Snackbar.Add("已选择语音素材", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"选择素材失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SelectVideoMaterial()
    {
        try
        {
            var materials = await MaterialSelectorService.ShowVideoSelectorAsync(false);
            if (materials?.Count > 0)
            {
                _selectedMaterial = materials[0];
                Snackbar.Add("已选择视频素材", Severity.Success);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"选择素材失败: {ex.Message}", Severity.Error);
        }
    }

    private void ClearSelectedMaterial()
    {
        _selectedMaterial = null;
        StateHasChanged();
    }

    /// <summary>
    /// 重置发送状态并显示警告消息
    /// </summary>
    private void ResetSendingStateWithWarning(string message)
    {
        Snackbar.Add(message, Severity.Warning);
        _isSending = false;
        StateHasChanged();
    }

}