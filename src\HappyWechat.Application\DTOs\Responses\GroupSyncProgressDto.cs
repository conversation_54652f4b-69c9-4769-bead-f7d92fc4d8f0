using HappyWechat.Domain.ValueObjects.Enums;

namespace HappyWechat.Application.DTOs.Responses;

/// <summary>
/// 群组同步进度DTO - 复用联系人同步的进度管理机制
/// </summary>
public class GroupSyncProgressDto
{
    /// <summary>
    /// 微信管理器ID
    /// </summary>
    public Guid WxManagerId { get; set; }

    /// <summary>
    /// 同步会话ID
    /// </summary>
    public string SyncSessionId { get; set; } = string.Empty;

    /// <summary>
    /// 同步状态
    /// </summary>
    public SyncStatus Status { get; set; }

    /// <summary>
    /// 总群组数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 已处理群组数量
    /// </summary>
    public int ProcessedCount { get; set; }

    /// <summary>
    /// 成功处理的群组数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败的群组数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 新增群组数量
    /// </summary>
    public int NewGroupCount { get; set; }

    /// <summary>
    /// 更新群组数量
    /// </summary>
    public int UpdatedGroupCount { get; set; }

    /// <summary>
    /// 当前处理的群组信息
    /// </summary>
    public string? CurrentGroup { get; set; }

    /// <summary>
    /// 当前处理的联系人
    /// </summary>
    public string? CurrentContact { get; set; }

    /// <summary>
    /// 当前处理阶段 (群组信息/群成员信息)
    /// </summary>
    public string? CurrentStage { get; set; }

    /// <summary>
    /// 群成员处理进度
    /// </summary>
    public GroupMemberSyncProgress? MemberProgress { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedTime { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// 预计完成时间
    /// </summary>
    public DateTime? EstimatedEndTime { get; set; }

    /// <summary>
    /// 当前阶段描述
    /// </summary>
    public string? CurrentPhase { get; set; }

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double ProgressPercentage => TotalCount > 0 ? (ProcessedCount * 100.0 / TotalCount) : 0;
}

/// <summary>
/// 群成员同步进度
/// </summary>
public class GroupMemberSyncProgress
{
    /// <summary>
    /// 总成员数量
    /// </summary>
    public int TotalMembers { get; set; }

    /// <summary>
    /// 已处理成员数量
    /// </summary>
    public int ProcessedMembers { get; set; }

    /// <summary>
    /// 当前处理的群组名称
    /// </summary>
    public string? CurrentGroupName { get; set; }

    /// <summary>
    /// 成员同步进度百分比
    /// </summary>
    public int MemberProgressPercentage => TotalMembers > 0 ? (ProcessedMembers * 100 / TotalMembers) : 0;
}