﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.Commons;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Commons;

namespace HappyWechat.Web.Controllers;

/// <summary>
/// 定时任务控制器 - 重构版本使用用户上下文注入
/// </summary>
[Route("api/schedule")]
public class ScheduleTaskController : BaseAuthenticatedController
{
    private readonly IScheduleTaskService _scheduleTaskService;

    public ScheduleTaskController(
        IScheduleTaskService scheduleTaskService,
        ICurrentUserContext userContext,
        ILogger<ScheduleTaskController> logger) : base(userContext, logger)
    {
        _scheduleTaskService = scheduleTaskService;
    }

    [HttpPost("createTask")]
    public async Task<ActionResult<ApiResponse<object>>> CreateTask(
        ScheduleTaskDto taskDto)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("CreateTask", new { DisplayName = taskDto.DisplayName, TaskType = taskDto.TaskType });
            
            await _scheduleTaskService.CreateTask(taskDto, CurrentUserId);
            
            Logger.LogInformation("创建定时任务成功 - UserId: {UserId}, DisplayName: {DisplayName}", 
                CurrentUserId, taskDto.DisplayName);
            
            return new object();
        });
    }

    [HttpPost("getTasks")]
    public async Task<ActionResult<ApiResponse<PageResponse<ScheduleTaskDto>>>> GetTasks(
        ScheduleTaskQuery query)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            if (query.PageQuery == null)
            {
                query.PageQuery = new PageQuery();
            }
            
            // 🔧 注释冗余的操作日志 - 减少日志噪音
            // LogOperation("GetTasks", new { query.PageQuery?.Page, query.PageQuery?.PageSize });

            var tasksPagination = await _scheduleTaskService.ListTasks(CurrentUserId, query);

            // 🔧 注释冗余的成功日志 - 减少日志噪音
            // Logger.LogInformation("获取定时任务列表成功 - UserId: {UserId}, Count: {Count}, Total: {Total}",
            //     CurrentUserId, tasksPagination.Items.Count, tasksPagination.TotalCount);
            
            return tasksPagination;
        });
    }

    [HttpPost("enabled")]
    public async Task<ActionResult<ApiResponse<object>>> Enabled(EnabledScheduleTaskCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("EnabledTask", new { TaskId = command.TaskId, Enabled = command.Enabled });
            
            await _scheduleTaskService.Enable(command);
            
            Logger.LogInformation("切换定时任务状态成功 - UserId: {UserId}, TaskId: {TaskId}, Enabled: {Enabled}", 
                CurrentUserId, command.TaskId, command.Enabled);
            
            return new object();
        });
    }
    
    [HttpPost("deleteTask")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteTask(DeleteTaskCommand command)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("DeleteTask", new { TaskId = command.TaskId });
            
            await _scheduleTaskService.DeleteTask(command.TaskId, CurrentUserId);
            
            Logger.LogInformation("删除定时任务成功 - UserId: {UserId}, TaskId: {TaskId}", 
                CurrentUserId, command.TaskId);
            
            return new object();
        });
    }
    
    [HttpPost("updateTask")]
    public async Task<ActionResult<ApiResponse<object>>> UpdateTask(ScheduleTaskDto taskDto)
    {
        return await ExecuteAuthenticatedOperation(async () =>
        {
            LogOperation("UpdateTask", new { TaskId = taskDto.Id, DisplayName = taskDto.DisplayName });
            
            await _scheduleTaskService.UpdateTask(taskDto, CurrentUserId);
            
            Logger.LogInformation("更新定时任务成功 - UserId: {UserId}, TaskId: {TaskId}, DisplayName: {DisplayName}", 
                CurrentUserId, taskDto.Id, taskDto.DisplayName);
            
            return new object();
        });
    }
}