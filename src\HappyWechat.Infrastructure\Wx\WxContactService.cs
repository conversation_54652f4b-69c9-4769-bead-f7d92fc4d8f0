using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Application.DTOs.Requests.Commands;
using HappyWechat.Application.DTOs.Responses;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Friends;
using HappyWechat.Application.Interfaces;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;
using HappyWechat.Infrastructure.MessageQueue.Simplified;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 微信联系人服务实现
/// </summary>
public class WxContactService : IWxContactService
{
    private readonly ILogger<WxContactService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IWxContactListRepository _contactListRepository;
    private readonly ContactBatchProcessor _batchProcessor;
    private readonly ContactSyncProgressTracker _progressTracker;
    private readonly ISimplifiedQueueService _queueService;

    public WxContactService(
        ILogger<WxContactService> logger,
        IServiceProvider serviceProvider,
        IWxContactListRepository contactListRepository,
        ContactBatchProcessor batchProcessor,
        ContactSyncProgressTracker progressTracker,
        ISimplifiedQueueService queueService)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _contactListRepository = contactListRepository;
        _batchProcessor = batchProcessor;
        _progressTracker = progressTracker;
        _queueService = queueService;
    }

    public async Task<ContactSyncProgressDto> GetContactDetailsAsync(GetContactDetailsCommand command)
    {
        try
        {
            // 🔧 注释冗余的开始获取日志 - 减少日志噪音
            // _logger.LogInformation("开始获取联系人详情 - WxManagerId: {WxManagerId}, ListTypes: {ListTypes}",
            //     command.WxManagerId, string.Join(",", command.ListTypes));

            // 1. 从数据库读取指定类型的联系人ID列表
            var friendsIds = new List<string>();
            var enterpriseIds = new List<string>();

            if (command.ListTypes.Contains(WxContactListType.Friends))
            {
                friendsIds = await _contactListRepository.GetContactIdsAsync(command.WxManagerId, WxContactListType.Friends);
                // 🔧 注释冗余的数量统计日志 - 减少日志噪音
                // _logger.LogInformation("获取到个人联系人ID数量: {Count}", friendsIds.Count);
            }

            if (command.ListTypes.Contains(WxContactListType.Enterprise))
            {
                enterpriseIds = await _contactListRepository.GetContactIdsAsync(command.WxManagerId, WxContactListType.Enterprise);
                // 🔧 注释冗余的数量统计日志 - 减少日志噪音
                // _logger.LogInformation("获取到企业联系人ID数量: {Count}", enterpriseIds.Count);
            }

            // 2. 检查是否有联系人需要同步
            if (!friendsIds.Any() && !enterpriseIds.Any())
            {
                _logger.LogWarning("没有找到需要同步的联系人 - WxManagerId: {WxManagerId}", command.WxManagerId);
                return new ContactSyncProgressDto
                {
                    WxManagerId = command.WxManagerId,
                    Status = SyncStatus.Completed,
                    TotalCount = 0,
                    ProcessedCount = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    StartTime = DateTime.UtcNow,
                    CompletedTime = DateTime.UtcNow,
                    ProgressPercentage = 100,
                    CurrentPhase = "没有需要同步的联系人"
                };
            }

            // 3. 创建同步会话ID
            var syncSessionId = Guid.NewGuid().ToString("N")[..12];

            // 4. 计算批次信息
            var friendBatches = _batchProcessor.CreateBatches(friendsIds, WxContactListType.Friends);
            var enterpriseBatches = _batchProcessor.CreateBatches(enterpriseIds, WxContactListType.Enterprise);

            var totalFriendBatches = friendBatches.Count;
            var totalEnterpriseBatches = enterpriseBatches.Count;

            // 🔧 注释冗余的批次计算完成日志 - 减少日志噪音
            // _logger.LogInformation("批次计算完成 - 个人联系人批次: {FriendBatches}, 企业联系人批次: {EnterpriseBatches}",
            //     totalFriendBatches, totalEnterpriseBatches);

            // 5. 初始化进度跟踪
            await _progressTracker.InitializeProgressAsync(
                command.WxManagerId,
                syncSessionId,
                friendsIds.Count,
                enterpriseIds.Count,
                totalFriendBatches,
                totalEnterpriseBatches);

            // 6. 将同步任务加入Redis队列
            await EnqueueSyncTasksAsync(command.WxManagerId, syncSessionId, friendBatches, enterpriseBatches);

            // 7. 返回初始进度信息
            var progress = await _progressTracker.GetProgressAsync(command.WxManagerId);
            if (progress != null)
            {
                // 🔧 提升为Warning级别 - 重要的同步任务完成日志
                _logger.LogWarning("✅ 联系人同步任务已加入队列 - WxManagerId: {WxManagerId}, SessionId: {SessionId}, 总数: {Total}",
                    command.WxManagerId, syncSessionId, progress.TotalCount);
                return progress;
            }

            // 如果获取进度失败，返回基础信息
            return new ContactSyncProgressDto
            {
                WxManagerId = command.WxManagerId,
                SyncSessionId = syncSessionId,
                Status = SyncStatus.InProgress,
                TotalCount = friendsIds.Count + enterpriseIds.Count,
                ProcessedCount = 0,
                SuccessCount = 0,
                FailedCount = 0,
                StartTime = DateTime.UtcNow,
                ProgressPercentage = 0,
                CurrentPhase = "同步任务已加入队列"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取联系人详情失败 - WxManagerId: {WxManagerId}", command.WxManagerId);

            // 标记同步失败
            await _progressTracker.MarkSyncFailedAsync(command.WxManagerId, ex.Message);

            return new ContactSyncProgressDto
            {
                WxManagerId = command.WxManagerId,
                Status = SyncStatus.Failed,
                ErrorMessage = ex.Message,
                StartTime = DateTime.UtcNow,
                CompletedTime = DateTime.UtcNow,
                CurrentPhase = "同步失败"
            };
        }
    }

    public async Task<ContactSyncProgressDto> ExecuteContactSyncAsync(GetContactDetailsCommand command)
    {
        // 🔧 注释冗余的委托日志 - 减少日志噪音
        // _logger.LogInformation("ExecuteContactSyncAsync 被调用，将委托给 GetContactDetailsAsync");
        return await GetContactDetailsAsync(command);
    }

    /// <summary>
    /// 将同步任务加入Redis队列
    /// </summary>
    /// <param name="wxManagerId">微信管理器ID</param>
    /// <param name="syncSessionId">同步会话ID</param>
    /// <param name="friendBatches">个人联系人批次</param>
    /// <param name="enterpriseBatches">企业联系人批次</param>
    /// <returns></returns>
    private async Task EnqueueSyncTasksAsync(
        Guid wxManagerId,
        string syncSessionId,
        List<List<string>> friendBatches,
        List<List<string>> enterpriseBatches)
    {
        try
        {
            var totalTasks = 0;

            // 1. 加入个人联系人同步任务
            for (int i = 0; i < friendBatches.Count; i++)
            {
                var batch = friendBatches[i];
                var message = new ContactSyncMessage
                {
                    WxManagerId = wxManagerId,
                    SyncSessionId = syncSessionId,
                    ListType = WxContactListType.Friends,
                    ContactIds = batch,
                    BatchIndex = i,
                    TotalBatches = friendBatches.Count,
                    BatchSize = batch.Count,
                    DelayConfig = new ContactSyncDelayConfig
                    {
                        MinDelayMs = 300,
                        MaxDelayMs = 1500,
                        EnableRandomDelay = true
                    },
                    RetryConfig = new ContactSyncRetryConfig
                    {
                        MaxRetryCount = 3,
                        RetryIntervalMs = 5000,
                        EnableExponentialBackoff = true
                    }
                };

                // 直接传递ContactSyncMessage，让SimplifiedQueueService进行包装
                var properties = new Dictionary<string, object>
                {
                    ["SyncSessionId"] = syncSessionId,
                    ["ListType"] = "Friends",
                    ["BatchIndex"] = i,
                    ["TotalBatches"] = friendBatches.Count
                };

                await _queueService.EnqueueAsync(wxManagerId, "contact_sync", message, 0, 3, properties);
                totalTasks++;

                _logger.LogDebug("个人联系人批次已加入队列 - WxManagerId: {WxManagerId}, 批次: {BatchIndex}/{TotalBatches}, 大小: {BatchSize}",
                    wxManagerId, i + 1, friendBatches.Count, batch.Count);
            }

            // 2. 加入企业联系人同步任务
            for (int i = 0; i < enterpriseBatches.Count; i++)
            {
                var batch = enterpriseBatches[i];
                var message = new ContactSyncMessage
                {
                    WxManagerId = wxManagerId,
                    SyncSessionId = syncSessionId,
                    ListType = WxContactListType.Enterprise,
                    ContactIds = batch,
                    BatchIndex = i,
                    TotalBatches = enterpriseBatches.Count,
                    BatchSize = batch.Count,
                    DelayConfig = new ContactSyncDelayConfig
                    {
                        MinDelayMs = 300,
                        MaxDelayMs = 1500,
                        EnableRandomDelay = true
                    },
                    RetryConfig = new ContactSyncRetryConfig
                    {
                        MaxRetryCount = 3,
                        RetryIntervalMs = 5000,
                        EnableExponentialBackoff = true
                    }
                };

                // 直接传递ContactSyncMessage，让SimplifiedQueueService进行包装
                var properties = new Dictionary<string, object>
                {
                    ["SyncSessionId"] = syncSessionId,
                    ["ListType"] = "Enterprise",
                    ["BatchIndex"] = i,
                    ["TotalBatches"] = enterpriseBatches.Count
                };

                await _queueService.EnqueueAsync(wxManagerId, "contact_sync", message, 0, 3, properties);
                totalTasks++;

                _logger.LogDebug("企业联系人批次已加入队列 - WxManagerId: {WxManagerId}, 批次: {BatchIndex}/{TotalBatches}, 联系人: {ContactId}",
                    wxManagerId, i + 1, enterpriseBatches.Count, batch.FirstOrDefault());
            }

            // 🔧 提升为Warning级别 - 重要的同步任务完成日志
            _logger.LogWarning("📋 所有同步任务已加入队列 - WxManagerId: {WxManagerId}, SessionId: {SessionId}, 总任务数: {TotalTasks}",
                wxManagerId, syncSessionId, totalTasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加入同步任务到队列失败 - WxManagerId: {WxManagerId}, SessionId: {SessionId}",
                wxManagerId, syncSessionId);
            throw;
        }
    }

    public async Task<bool> AddContactManuallyAsync(Guid userId, AddContactManuallyCommand command)
    {
        _logger.LogWarning("AddContactManuallyAsync not implemented yet");
        await Task.Delay(1);
        return false;
    }

    public async Task<ContactSyncProgressDto> GetSyncProgressAsync(Guid wxManagerId)
    {
        try
        {
            _logger.LogDebug("获取同步进度 - WxManagerId: {WxManagerId}", wxManagerId);

            var progress = await _progressTracker.GetProgressAsync(wxManagerId);
            if (progress != null)
            {
                _logger.LogDebug("获取同步进度成功 - WxManagerId: {WxManagerId}, 状态: {Status}, 进度: {Progress}%",
                    wxManagerId, progress.Status, progress.ProgressPercentage);
                return progress;
            }

            // 如果没有找到进度信息，返回默认状态
            _logger.LogDebug("未找到同步进度信息 - WxManagerId: {WxManagerId}", wxManagerId);
            return new ContactSyncProgressDto
            {
                WxManagerId = wxManagerId,
                Status = SyncStatus.NotStarted,
                TotalCount = 0,
                ProcessedCount = 0,
                SuccessCount = 0,
                FailedCount = 0,
                ProgressPercentage = 0,
                CurrentPhase = "未开始同步"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取同步进度失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new ContactSyncProgressDto
            {
                WxManagerId = wxManagerId,
                Status = SyncStatus.Failed,
                ErrorMessage = ex.Message,
                CurrentPhase = "获取进度失败"
            };
        }
    }

    public async Task<BatchOperationResult> BatchOperateContactsAsync(BatchContactOperationCommand command)
    {
        _logger.LogWarning("BatchOperateContactsAsync not implemented yet");
        await Task.Delay(1);
        return new BatchOperationResult();
    }

    public async Task<ImportResult> ImportContactsAsync(ImportContactsCommand command)
    {
        _logger.LogWarning("ImportContactsAsync not implemented yet");
        await Task.Delay(1);
        return new ImportResult();
    }

    public async Task<ExportResult> ExportContactsAsync(ExportContactsCommand command)
    {
        _logger.LogWarning("ExportContactsAsync not implemented yet");
        await Task.Delay(1);
        return new ExportResult();
    }

    public async Task<ContactStatisticsDto> GetContactStatisticsAsync(Guid wxManagerId)
    {
        _logger.LogWarning("GetContactStatisticsAsync not implemented yet");
        await Task.Delay(1);
        return new ContactStatisticsDto();
    }

    public async Task<bool> BatchUpdateContactsAsync(Guid userId, BatchUpdateContactCommand command)
    {
        _logger.LogWarning("BatchUpdateContactsAsync not implemented yet");
        await Task.Delay(1);
        return false;
    }

    public async Task SaveContactDetailPublicAsync(Guid wxManagerId, EYunGetContactData contactData, WxContactType contactType)
    {
        // 🔧 注释冗余的未实现警告日志 - 减少日志噪音
        // _logger.LogWarning("SaveContactDetailPublicAsync not implemented yet");
        await Task.Delay(1);
    }

    public async Task SaveEnterpriseContactDetailPublicAsync(Guid wxManagerId, EYunGetOpenImContactData contactData, WxContactType contactType)
    {
        // 🔧 注释冗余的未实现警告日志 - 减少日志噪音
        // _logger.LogWarning("SaveEnterpriseContactDetailPublicAsync not implemented yet");
        await Task.Delay(1);
    }
}