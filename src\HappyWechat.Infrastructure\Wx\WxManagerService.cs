using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.Wx;
using HappyWechat.Domain.ValueObjects.Enums;
using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.Wx;

/// <summary>
/// 微信管理器服务实现
/// </summary>
public class WxManagerService : IWxManagerService
{
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    private readonly ILogger<WxManagerService> _logger;

    public WxManagerService(
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        ILogger<WxManagerService> logger)
    {
        _dbContextFactory = dbContextFactory;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有已登录的微信管理器
    /// </summary>
    public async Task<List<WxManagerDto>> GetLoggedInManagersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var managers = await context.WxMangerEntities
                .Where(w => w.WxStatus == WxStatus.AlreadyLogIn && w.IsEnabled)
                .Select(w => new WxManagerDto
                {
                    Id = w.Id,
                    WxId = w.WcId ?? string.Empty,
                    NickName = w.NickName,
                    LastActiveTime = w.UpdatedTime,
                    IsOnline = true,
                    Status = w.Status ?? string.Empty,
                    CreatedTime = w.CreatedTime,
                    UpdatedTime = w.UpdatedTime
                })
                .ToListAsync(cancellationToken);

            // 🔧 注释冗余的Debug日志 - 减少日志噪音
            // _logger.LogDebug("获取已登录微信管理器数量: {Count}", managers.Count);
            return managers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取已登录微信管理器失败");
            return new List<WxManagerDto>();
        }
    }

    /// <summary>
    /// 根据ID获取微信管理器信息
    /// </summary>
    public async Task<WxManagerDto?> GetManagerByIdAsync(Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var manager = await context.WxMangerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => new WxManagerDto
                {
                    Id = w.Id,
                    WxId = w.WcId ?? string.Empty,
                    NickName = w.NickName,
                    LastActiveTime = w.UpdatedTime,
                    IsOnline = w.WxStatus == WxStatus.AlreadyLogIn,
                    Status = w.Status ?? string.Empty,
                    CreatedTime = w.CreatedTime,
                    UpdatedTime = w.UpdatedTime
                })
                .FirstOrDefaultAsync(cancellationToken);

            return manager;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取微信管理器失败: {WxManagerId}", wxManagerId);
            return null;
        }
    }

    /// <summary>
    /// 检查微信管理器是否在线
    /// </summary>
    public async Task<bool> IsManagerOnlineAsync(Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var isOnline = await context.WxMangerEntities
                .Where(w => w.Id == wxManagerId)
                .Select(w => w.WxStatus == WxStatus.AlreadyLogIn && w.IsEnabled)
                .FirstOrDefaultAsync(cancellationToken);

            return isOnline;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查微信管理器在线状态失败: {WxManagerId}", wxManagerId);
            return false;
        }
    }

    /// <summary>
    /// 获取所有微信管理器（包括离线的）
    /// </summary>
    public async Task<List<WxManagerDto>> GetAllManagersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var managers = await context.WxMangerEntities
                .Select(w => new WxManagerDto
                {
                    Id = w.Id,
                    WxId = w.WcId ?? string.Empty,
                    NickName = w.NickName,
                    LastActiveTime = w.UpdatedTime,
                    IsOnline = w.WxStatus == WxStatus.AlreadyLogIn,
                    Status = w.Status ?? string.Empty,
                    CreatedTime = w.CreatedTime,
                    UpdatedTime = w.UpdatedTime
                })
                .ToListAsync(cancellationToken);

            // 🔧 注释冗余的Debug日志 - 减少日志噪音
            // _logger.LogDebug("获取所有微信管理器数量: {Count}", managers.Count);
            return managers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有微信管理器失败");
            return new List<WxManagerDto>();
        }
    }
}