using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.Identity.Repositories;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.Database;

/// <summary>
/// 独立的DbContext工厂实现
/// 不依赖DI容器中的DbContextFactory，避免生命周期冲突
/// 支持读写分离和高性能场景
/// </summary>
public class IndependentDbContextFactory : IDbContextFactory, IDisposable
{
    private readonly string _writeConnectionString;
    private readonly string _readConnectionString;
    private readonly bool _enableReadWriteSeparation;
    private readonly ILogger<IndependentDbContextFactory> _logger;
    private readonly DbContextOptions<ApplicationDbContext> _writeOptions;
    private readonly DbContextOptions<ApplicationDbContext> _readOptions;
    
    // 性能监控
    private readonly ConcurrentDictionary<DatabaseOperationType, long> _operationCounts = new();
    private long _totalOperations;

    public IndependentDbContextFactory(
        IConfiguration configuration,
        ILogger<IndependentDbContextFactory> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 获取连接字符串
        _writeConnectionString = GetConnectionString(configuration, "Write");
        _readConnectionString = GetConnectionString(configuration, "Read");
        _enableReadWriteSeparation = configuration.GetValue<bool>("Database:EnableReadWriteSeparation", false);
        
        // 预构建DbContextOptions以提升性能
        _writeOptions = BuildDbContextOptions(_writeConnectionString, "Write");
        _readOptions = _enableReadWriteSeparation ? 
            BuildDbContextOptions(_readConnectionString, "Read") : 
            _writeOptions;
            
        _logger.LogInformation("独立DbContext工厂已初始化 - 读写分离: {EnableReadWriteSeparation}", _enableReadWriteSeparation);
    }

    /// <summary>
    /// 创建数据库上下文
    /// </summary>
    public async Task<ApplicationDbContext> CreateDbContextAsync(DatabaseOperationType operationType = DatabaseOperationType.Mixed)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            // 根据操作类型选择连接
            var options = ShouldUseReadConnection(operationType) ? _readOptions : _writeOptions;
            var connectionType = ShouldUseReadConnection(operationType) ? "Read" : "Write";
            
            var context = new ApplicationDbContext(options);
            
            // 记录性能指标
            RecordOperation(operationType);
            
            _logger.LogTrace("创建DbContext成功 - 类型: {OperationType}, 连接: {ConnectionType}, 耗时: {Duration}ms", 
                operationType, connectionType, stopwatch.ElapsedMilliseconds);
                
            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建DbContext失败 - 操作类型: {OperationType}, 耗时: {Duration}ms", 
                operationType, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 同步创建数据库上下文
    /// </summary>
    public ApplicationDbContext CreateDbContext(DatabaseOperationType operationType = DatabaseOperationType.Mixed)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            var options = ShouldUseReadConnection(operationType) ? _readOptions : _writeOptions;
            var connectionType = ShouldUseReadConnection(operationType) ? "Read" : "Write";
            
            var context = new ApplicationDbContext(options);
            
            RecordOperation(operationType);
            
            _logger.LogTrace("同步创建DbContext成功 - 类型: {OperationType}, 连接: {ConnectionType}, 耗时: {Duration}ms", 
                operationType, connectionType, stopwatch.ElapsedMilliseconds);
                
            return context;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步创建DbContext失败 - 操作类型: {OperationType}, 耗时: {Duration}ms", 
                operationType, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// 执行查询操作（使用读连接）
    /// </summary>
    public async Task<TResult> ExecuteQueryAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> query)
    {
        if (query == null) throw new ArgumentNullException(nameof(query));
        
        await using var context = await CreateDbContextAsync(DatabaseOperationType.Read);
        
        // 查询优化配置
        context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
        context.ChangeTracker.AutoDetectChangesEnabled = false;
        
        return await query(context);
    }

    /// <summary>
    /// 执行命令操作（使用写连接）
    /// </summary>
    public async Task<TResult> ExecuteCommandAsync<TResult>(Func<ApplicationDbContext, Task<TResult>> command)
    {
        if (command == null) throw new ArgumentNullException(nameof(command));
        
        await using var context = await CreateDbContextAsync(DatabaseOperationType.Write);
        
        // 使用事务确保数据一致性
        await using var transaction = await context.Database.BeginTransactionAsync();
        
        try
        {
            var result = await command(context);
            await transaction.CommitAsync();
            return result;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// 创建写操作的数据库上下文（兼容MaterialRepository）
    /// </summary>
    public async Task<ApplicationDbContext> CreateWriteContextAsync()
    {
        return await CreateDbContextAsync(DatabaseOperationType.Write);
    }

    /// <summary>
    /// 创建读操作的数据库上下文（兼容MaterialRepository）
    /// </summary>
    public async Task<ApplicationDbContext> CreateReadContextAsync()
    {
        return await CreateDbContextAsync(DatabaseOperationType.Read);
    }

    /// <summary>
    /// 同步创建写操作的数据库上下文
    /// </summary>
    public ApplicationDbContext CreateWriteContext()
    {
        return CreateDbContext(DatabaseOperationType.Write);
    }

    /// <summary>
    /// 同步创建读操作的数据库上下文
    /// </summary>
    public ApplicationDbContext CreateReadContext()
    {
        return CreateDbContext(DatabaseOperationType.Read);
    }

    /// <summary>
    /// 根据操作类型创建数据库上下文（兼容接口）
    /// </summary>
    public async Task<ApplicationDbContext> CreateContextAsync(bool isReadOnly = false)
    {
        var operationType = isReadOnly ? DatabaseOperationType.Read : DatabaseOperationType.Write;
        return await CreateDbContextAsync(operationType);
    }

    /// <summary>
    /// 获取工厂统计信息
    /// </summary>
    public IndependentDbContextFactoryStatistics GetStatistics()
    {
        return new IndependentDbContextFactoryStatistics
        {
            TotalOperations = _totalOperations,
            ReadOperations = _operationCounts.GetValueOrDefault(DatabaseOperationType.Read, 0),
            WriteOperations = _operationCounts.GetValueOrDefault(DatabaseOperationType.Write, 0),
            MixedOperations = _operationCounts.GetValueOrDefault(DatabaseOperationType.Mixed, 0),
            EnableReadWriteSeparation = _enableReadWriteSeparation,
            WriteConnectionString = MaskConnectionString(_writeConnectionString),
            ReadConnectionString = MaskConnectionString(_readConnectionString),
            LastStatisticsUpdate = DateTime.UtcNow
        };
    }

    #region 私有方法

    /// <summary>
    /// 获取连接字符串
    /// </summary>
    private static string GetConnectionString(IConfiguration configuration, string type)
    {
        return type switch
        {
            "Write" => configuration.GetConnectionString("DefaultConnection") ?? 
                      configuration["Database:WriteConnectionString"] ?? 
                      throw new InvalidOperationException($"写数据库连接字符串未配置"),
            "Read" => configuration["Database:ReadConnectionString"] ?? 
                     configuration.GetConnectionString("DefaultConnection") ?? 
                     configuration["Database:WriteConnectionString"] ?? 
                     throw new InvalidOperationException($"读数据库连接字符串未配置"),
            _ => throw new ArgumentException($"不支持的连接类型: {type}")
        };
    }

    /// <summary>
    /// 构建DbContext选项
    /// </summary>
    private DbContextOptions<ApplicationDbContext> BuildDbContextOptions(string connectionString, string connectionType)
    {
        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
        
        optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mysqlOptions =>
        {
            mysqlOptions.CommandTimeout(30);
            mysqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        });

        // 针对读连接进行优化
        if (connectionType == "Read")
        {
            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
        }

        // 性能优化
        optionsBuilder.EnableServiceProviderCaching();
        optionsBuilder.EnableSensitiveDataLogging(false); // 生产环境禁用
        
        return optionsBuilder.Options;
    }

    /// <summary>
    /// 判断是否应该使用读连接
    /// </summary>
    private bool ShouldUseReadConnection(DatabaseOperationType operationType)
    {
        return _enableReadWriteSeparation && operationType == DatabaseOperationType.Read;
    }

    /// <summary>
    /// 记录操作统计
    /// </summary>
    private void RecordOperation(DatabaseOperationType operationType)
    {
        Interlocked.Increment(ref _totalOperations);
        _operationCounts.AddOrUpdate(operationType, 1, (key, count) => count + 1);
    }

    /// <summary>
    /// 遮蔽连接字符串中的敏感信息
    /// </summary>
    private static string MaskConnectionString(string connectionString)
    {
        if (string.IsNullOrEmpty(connectionString))
            return string.Empty;
            
        // 简单遮蔽密码部分
        var parts = connectionString.Split(';');
        var maskedParts = parts.Select(part =>
        {
            if (part.Trim().StartsWith("Password=", StringComparison.OrdinalIgnoreCase))
                return "Password=***";
            return part;
        });
        
        return string.Join(";", maskedParts);
    }

    #endregion

    public void Dispose()
    {
        _logger.LogInformation("独立DbContext工厂已释放 - 总操作数: {TotalOperations}", _totalOperations);
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 独立DbContext工厂统计信息
/// </summary>
public class IndependentDbContextFactoryStatistics
{
    /// <summary>
    /// 总操作数
    /// </summary>
    public long TotalOperations { get; set; }

    /// <summary>
    /// 读操作数
    /// </summary>
    public long ReadOperations { get; set; }

    /// <summary>
    /// 写操作数
    /// </summary>
    public long WriteOperations { get; set; }

    /// <summary>
    /// 混合操作数
    /// </summary>
    public long MixedOperations { get; set; }

    /// <summary>
    /// 是否启用读写分离
    /// </summary>
    public bool EnableReadWriteSeparation { get; set; }

    /// <summary>
    /// 写连接字符串（已遮蔽）
    /// </summary>
    public string WriteConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 读连接字符串（已遮蔽）
    /// </summary>
    public string ReadConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 统计信息更新时间
    /// </summary>
    public DateTime LastStatisticsUpdate { get; set; }

    /// <summary>
    /// 读操作比例
    /// </summary>
    public double ReadOperationPercentage => TotalOperations > 0 ? (double)ReadOperations / TotalOperations * 100 : 0;

    /// <summary>
    /// 写操作比例
    /// </summary>
    public double WriteOperationPercentage => TotalOperations > 0 ? (double)WriteOperations / TotalOperations * 100 : 0;
}