using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Models;
using HappyWechat.Application.DTOs.Sync;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Wx;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Domain.ValueObjects.Enums;
using Microsoft.EntityFrameworkCore;

namespace HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers;

/// <summary>
/// 简化群组同步消费者 - 完全替代RedisGroupSyncConsumer
/// 处理群组同步队列的所有消息类型，支持GroupSyncMessage
/// </summary>
public class SimplifiedGroupSyncConsumer : SimplifiedQueueConsumerBase<GroupSyncMessage>
{
    public SimplifiedGroupSyncConsumer(
        IServiceProvider serviceProvider,
        ILogger<SimplifiedGroupSyncConsumer> logger)
        : base(serviceProvider, logger, "group_sync", batchSize: 15, maxConcurrency: 2, delayMs: 3000)
    {
    }

    protected override async Task<bool> ProcessMessageAsync(SimplifiedQueueMessage<GroupSyncMessage> message, CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var wxGroupSyncService = scope.ServiceProvider.GetRequiredService<WxGroupSyncService>();

            _logger.LogInformation("👥 处理群组同步消息 - MessageId: {MessageId}, WxManagerId: {WxManagerId}, Operation: {Operation}",
                message.Id, message.WxManagerId, message.Data.Operation);

            // 根据操作类型进行不同处理
            switch (message.Data.Operation)
            {
                case GroupSyncOperation.FullSync:
                    return await ProcessFullSyncAsync(message.Data, wxGroupSyncService, cancellationToken);
                case GroupSyncOperation.IncrementalSync:
                    return await ProcessIncrementalSyncAsync(message.Data, wxGroupSyncService, cancellationToken);
                case GroupSyncOperation.SingleGroup:
                    return await ProcessSingleGroupAsync(message.Data, wxGroupSyncService, cancellationToken);
                case GroupSyncOperation.GroupMembers:
                    return await ProcessGroupMembersAsync(message.Data, wxGroupSyncService, cancellationToken);
                case GroupSyncOperation.GroupUpdate:
                    return await ProcessGroupUpdateAsync(message.Data, wxGroupSyncService, cancellationToken);
                case GroupSyncOperation.GroupDelete:
                    return await ProcessGroupDeleteAsync(message.Data, wxGroupSyncService, cancellationToken);
                default:
                    _logger.LogWarning("⚠️ 未知群组同步操作 - Operation: {Operation}, MessageId: {MessageId}",
                        message.Data.Operation, message.Id);
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组同步处理失败 - MessageId: {MessageId}", message.Id);
            return false;
        }
    }

    private async Task<bool> ProcessFullSyncAsync(GroupSyncMessage data, WxGroupSyncService syncService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🔄 执行全量群组同步 - WxManagerId: {WxManagerId}, SessionId: {SessionId}",
                data.WxManagerId, data.SyncSessionId);

            // 🔧 集成进度跟踪器
            using var scope = _serviceProvider.CreateScope();
            var progressTracker = scope.ServiceProvider.GetRequiredService<GroupSyncProgressTracker>();

            // 获取群组总数并初始化进度
            var groupIds = await GetGroupIdsForSyncAsync(data.WxManagerId, scope);
            if (groupIds == null || !groupIds.Any())
            {
                _logger.LogWarning("⚠️ 未找到需要同步的群组 - WxManagerId: {WxManagerId}", data.WxManagerId);
                return true; // 没有群组需要同步，视为成功
            }

            await progressTracker.InitializeProgressAsync(data.WxManagerId, data.SyncSessionId ?? Guid.NewGuid().ToString(), groupIds.Count);

            // 执行同步并跟踪每个群组的进度
            var result = await syncService.PerformFullSyncWithProgressAsync(data.WxManagerId, progressTracker, cancellationToken);

            if (result.IsSuccess)
            {
                _logger.LogInformation("✅ 全量群组同步完成 - WxManagerId: {WxManagerId}, Count: {Count}",
                    data.WxManagerId, result.SyncedCount);
                return true;
            }
            else
            {
                _logger.LogError("❌ 全量群组同步失败 - WxManagerId: {WxManagerId}, Error: {Error}",
                    data.WxManagerId, result.ErrorMessage);

                // 标记同步失败
                await progressTracker.MarkSyncFailedAsync(data.WxManagerId, result.ErrorMessage ?? "未知错误");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 全量群组同步异常 - WxManagerId: {WxManagerId}", data.WxManagerId);

            // 标记同步失败
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var progressTracker = scope.ServiceProvider.GetRequiredService<GroupSyncProgressTracker>();
                await progressTracker.MarkSyncFailedAsync(data.WxManagerId, ex.Message);
            }
            catch (Exception trackEx)
            {
                _logger.LogError(trackEx, "❌ 标记群组同步失败状态异常");
            }

            return false;
        }
    }

    private async Task<bool> ProcessIncrementalSyncAsync(GroupSyncMessage data, WxGroupSyncService syncService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("📈 执行增量群组同步 - WxManagerId: {WxManagerId}", data.WxManagerId);

            var result = await syncService.PerformIncrementalSyncAsync(data.WxManagerId, null, cancellationToken);

            if (result.IsSuccess)
            {
                _logger.LogInformation("✅ 增量群组同步完成 - WxManagerId: {WxManagerId}, UpdatedCount: {Count}",
                    data.WxManagerId, result.UpdatedCount);
                return true;
            }
            else
            {
                _logger.LogError("❌ 增量群组同步失败 - WxManagerId: {WxManagerId}, Error: {Error}",
                    data.WxManagerId, result.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 增量群组同步异常 - WxManagerId: {WxManagerId}", data.WxManagerId);
            return false;
        }
    }

    private async Task<bool> ProcessSingleGroupAsync(GroupSyncMessage data, WxGroupSyncService syncService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("👥 同步单个群组 - WxManagerId: {WxManagerId}, GroupId: {GroupId}",
                data.WxManagerId, data.GroupId);

            if (string.IsNullOrEmpty(data.GroupId))
            {
                _logger.LogWarning("⚠️ 单个群组同步缺少GroupId - WxManagerId: {WxManagerId}", data.WxManagerId);
                return false;
            }

            var result = await syncService.SyncSingleGroupAsync(data.WxManagerId, data.GroupId, cancellationToken);

            if (result.IsSuccess)
            {
                _logger.LogDebug("✅ 单个群组同步完成 - GroupId: {GroupId}", data.GroupId);
                return true;
            }
            else
            {
                _logger.LogError("❌ 单个群组同步失败 - GroupId: {GroupId}, Error: {Error}",
                    data.GroupId, result.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 单个群组同步异常 - GroupId: {GroupId}", data.GroupId);
            return false;
        }
    }

    private async Task<bool> ProcessGroupMembersAsync(GroupSyncMessage data, WxGroupSyncService syncService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("👤 同步群组成员 - GroupId: {GroupId}", data.GroupId);

            if (string.IsNullOrEmpty(data.GroupId))
            {
                _logger.LogWarning("⚠️ 群组成员同步缺少GroupId");
                return false;
            }

            var result = await syncService.SyncGroupMembersAsync(data.WxManagerId, data.GroupId, cancellationToken);

            if (result.IsSuccess)
            {
                _logger.LogDebug("✅ 群组成员同步完成 - GroupId: {GroupId}, MemberCount: {Count}",
                    data.GroupId, result.MemberCount);
                return true;
            }
            else
            {
                _logger.LogError("❌ 群组成员同步失败 - GroupId: {GroupId}, Error: {Error}",
                    data.GroupId, result.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组成员同步异常 - GroupId: {GroupId}", data.GroupId);
            return false;
        }
    }

    private async Task<bool> ProcessGroupUpdateAsync(GroupSyncMessage data, WxGroupSyncService syncService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("📝 更新群组信息 - GroupId: {GroupId}", data.GroupId);

            if (string.IsNullOrEmpty(data.GroupId))
            {
                _logger.LogWarning("⚠️ 群组更新缺少GroupId");
                return false;
            }

            var result = await syncService.UpdateGroupAsync(data.WxManagerId, data.GroupId, null, cancellationToken);
            
            if (result.IsSuccess)
            {
                _logger.LogDebug("✅ 群组信息更新完成 - GroupId: {GroupId}", data.GroupId);
                return true;
            }
            else
            {
                _logger.LogError("❌ 群组信息更新失败 - GroupId: {GroupId}, Error: {Error}", 
                    data.GroupId, result.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组信息更新异常 - GroupId: {GroupId}", data.GroupId);
            return false;
        }
    }

    private async Task<bool> ProcessGroupDeleteAsync(GroupSyncMessage data, WxGroupSyncService syncService, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("🗑️ 删除群组 - GroupId: {GroupId}", data.GroupId);

            if (string.IsNullOrEmpty(data.GroupId))
            {
                _logger.LogWarning("⚠️ 群组删除缺少GroupId");
                return false;
            }

            var result = await syncService.DeleteGroupAsync(data.WxManagerId, data.GroupId, cancellationToken);

            if (result.IsSuccess)
            {
                _logger.LogDebug("✅ 群组删除完成 - GroupId: {GroupId}", data.GroupId);
                return true;
            }
            else
            {
                _logger.LogError("❌ 群组删除失败 - GroupId: {GroupId}, Error: {Error}",
                    data.GroupId, result.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 群组删除异常 - GroupId: {GroupId}", data.GroupId);
            return false;
        }
    }

    /// <summary>
    /// 获取需要同步的群组ID列表
    /// </summary>
    private async Task<List<string>?> GetGroupIdsForSyncAsync(Guid wxManagerId, IServiceScope scope)
    {
        try
        {
            var contactListRepository = scope.ServiceProvider.GetRequiredService<IWxContactListRepository>();
            var groupIds = await contactListRepository.GetContactIdsAsync(wxManagerId, WxContactListType.Chatrooms);
            return groupIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取群组ID列表失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return null;
        }
    }
}